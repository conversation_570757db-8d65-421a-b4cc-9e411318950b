import { BranchAppSidebar } from "@/components/sidebar/branchAdmin-app-sidebar";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import React from "react";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";

export default function UserDashboardLayout({ children }: { children: React.ReactNode }) {
    return (
        <SidebarProvider>
            <BranchAppSidebar />
            <SidebarInset>
                <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
                    <SidebarTrigger className="-ml-1" />
                    <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
                    <h1 className="text-xl font-bold">User Dashboard</h1>
                </header>
                {children}
            </SidebarInset>
        </SidebarProvider>
    );
}

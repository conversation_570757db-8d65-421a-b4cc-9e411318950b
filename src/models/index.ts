import sequelize from "@/lib/sequelize";

import Announcement from './schemas/announcements';
import Course from './schemas/courses';
import Shift from './schemas/shifts';
import Branch from './schemas/branches';
import Classes from './schemas/classes';
import Enrollment from './schemas/entrollments';
import User from './schemas/users';
import Payroll from './schemas/payrolls';
import Role from './schemas/roles';

// user --> role
Role.hasMany(User, { foreignKey: "role_id", as: "users" });
User.belongsTo(Role, { foreignKey: "role_id", as: "roles" });

// User --> Branch
User.hasMany(Branch, { foreignKey: "manager_admin_id" });
Branch.belongsTo(User, { foreignKey: "manager_admin_id" });

// user --> payroll
User.hasMany(Payroll, { foreignKey: "user_id" });
Payroll.belongsTo(User, { foreignKey: "user_id" });

// branch --> course
Branch.hasMany(Course, { foreignKey: "branch_id" });
Course.belongsTo(Branch, { foreignKey: "branch_id" });

// branch --> shift
Branch.hasMany(Shift, { foreignKey: "branch_id" });
Shift.belongsTo(Branch, { foreignKey: "branch_id" });

// branch --> classes
Branch.hasMany(Classes, { foreignKey: "branch_id" })
Classes.belongsTo(Branch, { foreignKey: "branch_id" })

// branch --> payrolls
Branch.hasMany(Payroll, { foreignKey: "branch_id" });
Payroll.belongsTo(Branch, { foreignKey: "branch_id" });

// branch --> enrollment
Branch.hasMany(Enrollment, { foreignKey: "branch_id" });
Enrollment.belongsTo(Branch, { foreignKey: "branch_id" });

// branch --> announcements
Branch.hasMany(Announcement, { foreignKey: "branch_id" });
Announcement.belongsTo(Branch, { foreignKey: "branch_id" });

// course --> announcements
Course.hasMany(Announcement, { foreignKey: "course_id" });
Announcement.belongsTo(Course, { foreignKey: "course_id" });



// course --> classes
Course.hasMany(Classes, { foreignKey: "course_id" });
Classes.belongsTo(Course, { foreignKey: "course_id" });

// classes --> users [instructor]
User.hasMany(Classes, { foreignKey: "instructor_id" });
Classes.belongsTo(User, { foreignKey: "instructor_id" });

// enrollment --> classes
Classes.hasMany(Enrollment, { foreignKey: "class_id" });
Enrollment.belongsTo(Classes, { foreignKey: "class_id" });

// course --> Enrollment
Course.hasMany(Enrollment, { foreignKey: "course_id" });
Enrollment.belongsTo(Course, { foreignKey: "course_id" });

// user --> enrollment
User.hasMany(Enrollment, { foreignKey: "user_id" });
Enrollment.belongsTo(User, { foreignKey: "user_id" });

export {
    sequelize,
    User,
    Role,
    Branch,
    Shift,
    Course,
    Classes,
    Enrollment,
    Payroll,
    Announcement
};
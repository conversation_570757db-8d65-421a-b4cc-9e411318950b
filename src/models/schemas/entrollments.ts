import sequelize from "@/lib/sequelize";
import { DataTypes, Model, Optional } from "sequelize";
import Course from "./courses";
import User from "./users";
import Classes from "./classes";

export interface EnrollmentAttributes {
    id: number;
    class_id: number;
    course_id: number;
    enrollment_date: Date;
    discount_type: "percentage" | "amount";
    discount: number;
    enrollment_amount: number;
    enrollment_status: "expired" | "not_expired";
    user_id: number;
    due_amount: number;
    enrollment_expired: Date;
}

export interface EnrollmentCreationAttributes extends Optional<EnrollmentAttributes, "id" | "enrollment_status"> { }

const Enrollment = sequelize.define<Model<EnrollmentAttributes, EnrollmentCreationAttributes>>(
    "enrollments",
    {
        id: {
            type: DataTypes.INTEGER.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
        },
        class_id: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
            references: {
                model: "classes",
                key: "id",
            },
            onUpdate: "CASCADE",
            onDelete: "RESTRICT",
        },
        course_id: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
            references: {
                model: "courses",
                key: "id",
            },
            onUpdate: "CASCADE",
            onDelete: "RESTRICT",
        },
        enrollment_date: {
            type: DataTypes.DATEONLY,
            allowNull: false,
        },
        discount_type: {
            type: DataTypes.ENUM("percentage", "amount"),
            allowNull: true,
        },
        discount: {
            type: DataTypes.FLOAT.UNSIGNED,
            allowNull: true,
            defaultValue: 0,
        },
        enrollment_amount: {
            type: DataTypes.FLOAT.UNSIGNED,
            allowNull: false,
        },
        enrollment_status: {
            type: DataTypes.ENUM("expired", "not_expired"),
            allowNull: false,
            defaultValue: "not_expired",
        },
        user_id: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
            references: {
                model: "users",
                key: "id"
            },
            onUpdate: "CASCADE",
            onDelete: "RESTRICT",
        },
        due_amount: {
            type: DataTypes.FLOAT.UNSIGNED,
            allowNull: false,
            defaultValue: 0,
        },
        enrollment_expired: {
            type: DataTypes.DATE,
            allowNull: false,
        },
    },
    {
        tableName: "enrollments",
        hooks: {
            // Automatically update enrollment_status if enrollment_expired has passed
            beforeSave: (enrollment) => {
                if (new Date() > new Date(enrollment.enrollment_expired)) {
                    enrollment.enrollment_status = "expired";
                } else {
                    enrollment.enrollment_status = "not_expired";
                }
            },
        },
    }
);


export default Enrollment;

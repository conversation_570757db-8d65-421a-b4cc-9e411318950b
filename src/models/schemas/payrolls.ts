import { DataType, DataTypes, Model, Optional } from "sequelize";
import sequelize from "@/lib/sequelize";
import Institute from "./branches";
import User from "./users";
import Branch from "./branches";

export interface payrollAttributes {
    id: number,
    user_id: number,
    branch_id: number,
    paycheck_date: Date,
    bonus: number,
    remarks: string,
}

export interface payrollCreationAttributes extends Optional<payrollAttributes, "id"> { }

const Payroll = sequelize.define<Model<payrollAttributes, payrollCreationAttributes>>(
    "payrolls", {
    id: {
        type: DataTypes.INTEGER.UNSIGNED,
        autoIncrement: true,
        primaryKey: true
    },
    user_id: {
        type: DataTypes.INTEGER.UNSIGNED,
        allowNull: false,
        references: {
            model: "users",
            key: "id"
        }
    },
    branch_id: {
        type: DataTypes.INTEGER.UNSIGNED,
        allowNull: false,
        references: {
            model: "branches",
            key: "id"
        }
    },
    paycheck_date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
    },
    bonus: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0
    },
    remarks: {
        type: DataTypes.STRING,
        allowNull: false,
    },
}, {
    tableName: "payrolls"
})

export default Payroll;
import { DataTypes, Model, Optional } from "sequelize";
import sequelize from "@/lib/sequelize";

export interface UserAttributes {
    id: number;
    name: string;
    email: string;
    role_id: number;
    phone: string;
    password: string,
    status: number;
}

export interface UserCreationAttributes extends Optional<UserAttributes, "id"> { }

const User = sequelize.define<Model<UserAttributes, UserCreationAttributes>>(
    "users",
    {
        id: {
            type: DataTypes.INTEGER.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
        },
        name: {
            type: DataTypes.STRING(128),
            allowNull: false,
        },
        email: {
            type: DataTypes.STRING(128),
            allowNull: false,
            unique: true,
        },
        phone: {
            type: DataTypes.STRING(20),
            allowNull: false,
        },
        password: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        role_id: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
            references: {
                model: "roles",
                key: "id",
            },
            onUpdate: "CASCADE",
            onDelete: "RESTRICT",
        },
        status: {
            type: DataTypes.ENUM("ACTIVE", "INACTIVE"),
            allowNull: false,
        }
    },
    {
        tableName: "users",
    }
);

export default User;

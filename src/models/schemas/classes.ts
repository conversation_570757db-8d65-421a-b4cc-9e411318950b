import sequelize from "@/lib/sequelize";
import { DataTypes, Model, Optional } from "sequelize";

export interface ClassAttributes {
    id: number;
    course_id: number;
    instructor_id: number;
    time: string;
    classType: "ONLINE" | "ONINSTITUDE";
    description: string;
    branch_id: number;
    shift_id: number;
    status: number;
}

export interface ClassCreationAttributes extends Optional<ClassAttributes, "id"> { }

const Classes = sequelize.define<Model<ClassAttributes, ClassCreationAttributes>>(
    "classes",
    {
        id: {
            type: DataTypes.INTEGER.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
        },
        course_id: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
            references: {
                model: "courses",
                key: "id",
            },
            onUpdate: "CASCADE",
            onDelete: "RESTRICT",
        },
        instructor_id: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
            references: {
                model: "users",
                key: "id",
            },
            onUpdate: "CASCADE",
            onDelete: "RESTRICT",
        },
        time: {
            type: DataTypes.STRING(50),
            allowNull: false,
        },
        classType: {
            type: DataTypes.ENUM("ONLINE", "ONINSTITUDE"),
            allowNull: false,
            defaultValue: "ONINSTITUDE",
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        branch_id: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
            references: {
                model: "branches",
                key: "id",
            },
            onUpdate: "CASCADE",
            onDelete: "RESTRICT",
        },
        shift_id: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
            references: {
                model: "shifts",
                key: "id",
            },
            onUpdate: "CASCADE",
            onDelete: "RESTRICT",
        },
        status: {
            type: DataTypes.TINYINT,
            allowNull: false,
            defaultValue: 1,
        },
    },
    {
        tableName: "classes",
    }
);

export default Classes;

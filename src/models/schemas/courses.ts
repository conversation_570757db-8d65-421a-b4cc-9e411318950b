import { DataTypes, Model, Optional } from "sequelize";
import sequelize from "@/lib/sequelize";

// Course attributes interface
export interface CourseAttributes {
    id: number;
    branch_id: number;
    course_title: string;
    course_fees: number;
    course_duration: string;
    course_start_date: string;
    course_description: string;
    course_status: number;
}

// Optional fields for creation
export interface CourseCreationAttributes extends Optional<CourseAttributes, "id"> { }

// Define Course model
const Course = sequelize.define<Model<CourseAttributes, CourseCreationAttributes>>(
    "courses",
    {
        id: {
            type: DataTypes.INTEGER.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
        },
        branch_id: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
            references: { model: "branches", key: "id" },
            onUpdate: "CASCADE",
            onDelete: "RESTRICT",
        },
        course_title: {
            type: DataTypes.STRING(128),
            allowNull: false,
        },
        course_fees: {
            type: DataTypes.FLOAT.UNSIGNED,
            allowNull: false,
        },
        course_duration: {
            type: DataTypes.STRING(50),
            allowNull: false,
        },
        course_start_date: {
            type: DataTypes.DATEONLY,
            allowNull: false,
        },
        course_description: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        course_status: {
            type: DataTypes.TINYINT,
            allowNull: false,
            defaultValue: 1,
        },
    },
    {
        tableName: "courses",
    }
);


export default Course;

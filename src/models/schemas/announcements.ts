import sequelize from "@/lib/sequelize";
import { DataTypes, Model, Optional } from "sequelize";

export interface AnnouncementAttributes {
    id: number;
    course_id: number;
    class_id: number;
    uploaded_date: Date;
    materials: string; // JSON string storing images/videos URLs
    title: string;
    description: string;
}

export interface AnnouncementCreationAttributes extends Optional<AnnouncementAttributes, "id"> { }

const Announcement = sequelize.define<Model<AnnouncementAttributes, AnnouncementCreationAttributes>>(
    "announcements",
    {
        id: {
            type: DataTypes.INTEGER.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
        },
        course_id: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
            references: {
                model: "courses",
                key: "id",
            },
            onUpdate: "CASCADE",
            onDelete: "RESTRICT",
        },
        class_id: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
            references: {
                model: "classes",
                key: "id",
            },
            onUpdate: "CASCADE",
            onDelete: "RESTRICT",
        },
        uploaded_date: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        materials: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        title: {
            type: DataTypes.STRING(128),
            allowNull: false,
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
    },
    {
        tableName: "announcements",
    }
);

export default Announcement;

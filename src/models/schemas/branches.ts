import { DataTypes, Model, Optional } from "sequelize";
import sequelize from "@/lib/sequelize";
import User from "./users";

// Interface for Branch
export interface BranchAttributes {
    id: number;
    manager_admin_id: number;
    branch_name: string;
    branch_email: string;
    branch_phone: string;
    referral_link: string;
    branch_status: number;
    branch_address: string;
}

// Optional fields for creation
export interface BranchCreationAttributes extends Optional<BranchAttributes, "id"> { }

// Define Branch model
const Branch = sequelize.define<Model<BranchAttributes, BranchCreationAttributes>>(
    "branches",
    {
        id: {
            type: DataTypes.INTEGER.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
        },
        manager_admin_id: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
            references: {
                model: "users",
                key: "id",
            },
            onUpdate: "CASCADE",
            onDelete: "RESTRICT",
        },
        branch_name: {
            type: DataTypes.STRING(128),
            allowNull: false,
        },
        branch_email: {
            type: DataTypes.STRING(128),
            allowNull: false,
        },
        branch_phone: {
            type: DataTypes.STRING(20),
            allowNull: false,
        },
        branch_address: {
            type: DataTypes.STRING(),
            allowNull: false,
        },
        referral_link: {
            type: DataTypes.STRING(255),
            allowNull: false,
        },
        branch_status: {
            type: DataTypes.TINYINT,
            allowNull: false,
        },
    },
    {
        tableName: "branches",
    }
);


export default Branch;

import sequelize from "@/lib/sequelize";
import { Optional, Model, DataTypes } from "sequelize";

export interface RoleAttribute {
    id: number,
    role: "SUPERADMIN" | "BRANCH" | "USER" | "TEACHER" | "COUNSELOR" | "MANAGER";
    description: string,
}

export interface RoleCreationAttribute extends Optional<RoleAttribute, "id"> { }

const Role = sequelize.define<Model<RoleAttribute, RoleCreationAttribute>>(
    "roles",
    {
        id: {
            type: DataTypes.INTEGER.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
        },
        role: {
            type: DataTypes.ENUM("<PERSON><PERSON><PERSON>ADMI<PERSON>", "BRANCH", "USER", "<PERSON><PERSON><PERSON><PERSON>", "COUNSE<PERSON><PERSON>", "MANAGER"),
            allowNull: false,
            defaultValue: "USER",
        },
        description: {
            type: DataTypes.STRING(128),
            allowNull: false,

        }
    }, {
    tableName: "roles"
}
)

export default Role;
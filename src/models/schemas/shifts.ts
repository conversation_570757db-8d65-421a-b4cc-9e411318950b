import sequelize from "@/lib/sequelize";
import { DataTypes, Model, Optional } from "sequelize";
import Branch from "./branches"; // reference branch model
import Classes from "./classes";

// Shift attributes
export interface ShiftAttributes {
    id: number;
    branch_id: number;
    shift_title: string;
    shift_time: string;
}

// Optional fields for creation
export interface ShiftCreationAttributes extends Optional<ShiftAttributes, "id"> { }

const Shift = sequelize.define<Model<ShiftAttributes, ShiftCreationAttributes>>(
    "shifts",
    {
        id: {
            type: DataTypes.INTEGER.UNSIGNED,
            autoIncrement: true,
            primaryKey: true,
        },
        branch_id: {
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
            references: {
                model: "branches",
                key: "id",
            },
            onUpdate: "CASCADE",
            onDelete: "RESTRICT",
        },
        shift_title: {
            type: DataTypes.STRING(128),
            allowNull: false,
        },
        shift_time: {
            type: DataTypes.STRING(50), // or DataTypes.TIME if you prefer
            allowNull: false,
        },
    },
    {
        tableName: "shifts",
    }
);

export default Shift;

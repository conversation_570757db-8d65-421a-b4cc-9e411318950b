import { NextRequest, NextResponse } from "next/server";

type Handler = (req: NextRequest) => Promise<NextResponse>;

export const catchAsync = (fn: Handler) => {
    return async (req: NextRequest, res: NextResponse) => {
        try {
            return await fn(req);
        } catch (error: any) {
            if (error.name === "SequelizeUniqueConstraintError") {
                const errorMessage = Object.entries(error.fields)
                    .map(([key, value]) => `Duplicate ${key} inserted: ${value}`)
                    .join(", ");

                return res.status(409).json({ statusTitle: "Duplicate Entry", message: errorMessage });
            }

            // // Default error response
            // return NextResponse.json(
            //     { status: "error", message: error.message || "Internal Server Error" },
            //     { status: 500 }
            // );
        }
    };
};

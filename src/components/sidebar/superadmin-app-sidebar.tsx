"use client"

import * as React from "react"
import {
  LayoutDashboard,    // System Overview
  Building2,          // Institute Performance
  Users,              // Admin Activity
  GraduationCap,      // Student Insights
  FileText,           // Document Status
  DollarSign,         // Financial Overview
  BookOpenCheck,      // Content Analytics
  MessageSquare,      // Post Analytics (fixed)
  Video,              // Class Attendance Overview
  LineChart,          // System Analytics
  Download,           // Data Export Configuration
  Languages,          // Language Configuration
  FlaskConical,       // Test Environment Management
  Settings,           // System Configuration
  Lock,               // Security Management
  GalleryVerticalEnd,
  AudioWaveform,
  Command,
  Frame,
} from "lucide-react"

import { NavMain } from "@/components/nav-main"
import { NavProjects } from "@/components/nav-projects"
import { NavUser } from "@/components/nav-user"
import { TeamSwitcher } from "@/components/team-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"

// Sidebar data
const data = {
  user: {
    name: "nihon",
    email: "<EMAIL>",
    avatar: "",
  },
  teams: [
    {
      name: "Nihon Inc",
      logo: GalleryVerticalEnd,
      plan: "Enterprise",
    },
    {
      name: "Nihon Corp.",
      logo: AudioWaveform,
      plan: "Startup",
    },
    {
      name: "Evil Corp.",
      logo: Command,
      plan: "Free",
    },
  ],
  navMain: [
    {
      title: "System Overview",
      url: "/superadmin/dashboard/system-overview",
      icon: LayoutDashboard,
    },
    {
      title: "Institute Performance",
      url: "/superadmin/dashboard/institute-performance",
      icon: Building2,
    },
    {
      title: "Admin Activity",
      url: "/superadmin/dashboard/admin-activity",
      icon: Users,
    },
    {
      title: "Student Insights",
      url: "/superadmin/dashboard/student-insights",
      icon: GraduationCap,
    },
    {
      title: "Document Status",
      url: "/superadmin/dashboard/document-status",
      icon: FileText,
    },
    {
      title: "Financial Overview",
      url: "/superadmin/dashboard/financial-overview",
      icon: DollarSign,
    },
    {
      title: "Content Analytics",
      url: "/superadmin/dashboard/content-analytics",
      icon: BookOpenCheck,
    },
    {
      title: "Post Analytics",
      url: "/superadmin/dashboard/post-analytics",
      icon: MessageSquare,
    },
    {
      title: "Class Attendance Overview",
      url: "/superadmin/dashboard/class-attendance",
      icon: Video,
    },
    {
      title: "System Analytics",
      url: "/superadmin/dashboard/system-analytics",
      icon: LineChart,
    },
    {
      title: "Data Export Config",
      url: "/superadmin/dashboard/export-config",
      icon: Download,
    },
    {
      title: "Language Config",
      url: "/superadmin/dashboard/language-config",
      icon: Languages,
    },
    {
      title: "Test Environment",
      url: "/superadmin/dashboard/test-env",
      icon: FlaskConical,
    },
    {
      title: "System Config",
      url: "/superadmin/dashboard/system-config",
      icon: Settings,
    },
    {
      title: "Security Management",
      url: "/superadmin/dashboard/security",
      icon: Lock,
    },
  ],
  projects: [
    {
      name: "Consultancies",
      url: "#",
      icon: Frame,
    },
  ],
}

export function SuperAdminAppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
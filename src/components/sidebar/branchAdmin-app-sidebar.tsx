"use client"

import * as React from "react"
import {
  LayoutDashboard,
  Building2,
  Users,
  GraduationCap,
  FileText,
  DollarSign,
  BookOpen,
  MessageSquare,
  Video,
  LineChart,
  Download,
  Languages,
  FlaskConical,
  Settings,
  Lock,
  GalleryVerticalEnd,
  AudioWaveform,
  Command,
  Frame,
  ChevronDown,
} from "lucide-react"

import { NavProjects } from "@/components/nav-projects"
import { NavUser } from "@/components/nav-user"
import { TeamSwitcher } from "@/components/team-switcher"
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarRail } from "@/components/ui/sidebar"
import Link from "next/link"

type NavItem = {
  title: string
  url?: string
  icon: any
  items?: NavItem[]
}

// Sidebar data
const data = {
  user: {
    name: "nihon",
    email: "<EMAIL>",
    avatar: "",
  },
  teams: [
    { name: "Nihon Inc", logo: GalleryVerticalEnd, plan: "Enterprise" },
    { name: "Nihon Corp.", logo: AudioWaveform, plan: "Startup" },
    { name: "Evil Corp.", logo: Command, plan: "Free" },
  ],
  navMain: [
    {
      title: "Dashboard Overview",
      url: "/branchadmin",
      icon: LayoutDashboard,
    },
    {
      title: "Institute Management",
      url: "/branchadmin/institute",
      icon: Building2,
      items: [
        { title: "Add Institute", url: "/branchadmin/institute/add", icon: Building2 },
        { title: "All Institutes", url: "/branchadmin/institute", icon: Building2 },
      ],
    },
    {
      title: "Staff Management",
      url: "/branchadmin/staff",
      icon: Users,
      items: [
        { title: "Add Staff", url: "/branchadmin/staff/add", icon: Users },
        { title: "Staff List", url: "/branchadmin/staff", icon: Users },
      ],
    },
    {
      title: "Student Management",
      url: "/branchadmin/students",
      icon: GraduationCap,
      items: [
        { title: "Add Student", url: "/branchadmin/students/add", icon: GraduationCap },
        { title: "Student List", url: "/branchadmin/students", icon: GraduationCap },
      ],
    },
    {
      title: "Document Verification",
      url: "/branchadmin/documents",
      icon: FileText,
    },
    {
      title: "Payments & Subscriptions",
      url: "/branchadmin/payments",
      icon: DollarSign,
    },
    {
      title: "Learning Content Update",
      url: "/branchadmin/learning-content",
      icon: BookOpen,
    },
    {
      title: "Post Management",
      url: "/branchadmin/posts",
      icon: MessageSquare,
    },
    {
      title: "Online Class Scheduling",
      url: "/branchadmin/classes",
      icon: Video,
    },
    {
      title: "Institute Analytics",
      url: "/branchadmin/analytics",
      icon: LineChart,
    },
    {
      title: "Data Export",
      url: "/branchadmin/export",
      icon: Download,
    },
    {
      title: "Language Preferences",
      url: "/branchadmin/language",
      icon: Languages,
    },
    {
      title: "Test Environment Access",
      url: "/branchadmin/test-env",
      icon: FlaskConical,
    },
    {
      title: "Institute Settings",
      url: "/branchadmin/settings",
      icon: Settings,
    },
    {
      title: "Security Monitoring",
      url: "/branchadmin/security",
      icon: Lock,
    },
  ],
  projects: [{ name: "Consultancies", url: "#", icon: Frame }],
}

function NavMain({ items }: { items: NavItem[] }) {
  const [open, setOpen] = React.useState<string | null>(null)

  return (
    <div className="space-y-1">
      {items.map((item) => (
        <div key={item.title}>
          <button
            onClick={() => setOpen(open === item.title ? null : item.title)}
            className="flex w-full items-center justify-between px-3 py-2 rounded-lg hover:bg-muted"
          >
            <div className="flex items-center gap-2">
              <item.icon className="h-4 w-4" />
              <span>{item.title}</span>
            </div>
            {item.items && (
              <ChevronDown className={`h-4 w-4 transition-transform ${open === item.title ? "rotate-180" : ""}`} />
            )}
          </button>

          {item.items && open === item.title && (
            <div className="ml-6 mt-1 space-y-1">
              {item.items.map((sub) => (
                <Link
                  key={sub.title}
                  href={sub.url || "#"}
                  className="flex items-center gap-2 px-3 py-1 rounded-lg hover:bg-accent"
                >
                  <sub.icon className="h-3 w-3" />
                  <span className="text-sm">{sub.title}</span>
                </Link>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

export function BranchAppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

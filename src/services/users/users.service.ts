import User from "@/models/schemas/users";
import { statusFunction } from "@/utils/statusFunction";
import * as argon2 from "argon2";
import { NextResponse } from "next/server";


export async function getAllUsers() {
    return await User.findAll();
}

export async function createUsersService({ name, email, phone, password, role_id, status }: createUserAttributes) {

    console.log(name, email, phone, password, role_id, status)

    const checkExistingUsers = await User.findOne({ where: { email: email } });

    if (checkExistingUsers) {
        console.log("exists")
        throw new Error("USER_EXISTS");
        // return NextResponse.json({ statusTitle: "Duplicate Entry" }, { status: 200 });

        // return statusFunction(201, { status: "success" });
        // return statusFunction(409, { message: "Data Already Exists" })
    }

    const hashedPassword = await argon2.hash(password);

    const uploadUsers = await User.create({
        name: name,
        email: email,
        phone: phone,
        password: hashedPassword,
        role_id: role_id,
        status: status
    })

    console.log(uploadUsers)
    return uploadUsers;
}
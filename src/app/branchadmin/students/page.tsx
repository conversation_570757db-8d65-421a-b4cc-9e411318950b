"use client"

import { useState } from "react"
import UserDashboardLayout from "@/layouts/user-dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Users, Search, Filter, Eye, Edit, Trash2, GraduationCap, Mail, Phone, Calendar } from "lucide-react"

interface Student {
  id: string
  name: string
  email: string
  phone: string
  course: string
  enrollmentDate: string
  status: "active" | "inactive" | "completed" | "suspended"
  paidAmount: number
  remainingAmount: number
  profileImage?: string
}

export default function StudentsPage() {
  const [searchTerm, setSearchTerm] = useState("")

  // Mock student data based on your database schema
  const students: Student[] = [
    {
      id: "1",
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "+81-90-1234-5678",
      course: "N5 Japanese",
      enrollmentDate: "2024-01-15",
      status: "active",
      paidAmount: 800,
      remainingAmount: 200,
      profileImage: "/images/student.png",
    },
    {
      id: "2",
      name: "<PERSON><PERSON>",
      email: "<EMAIL>",
      phone: "+81-90-2345-6789",
      course: "N4 Japanese",
      enrollmentDate: "2024-01-10",
      status: "active",
      paidAmount: 1200,
      remainingAmount: 0,
      profileImage: "/images/student.png",
    },
    {
      id: "3",
      name: "Hiroshi Yamamoto",
      email: "<EMAIL>",
      phone: "+81-90-3456-7890",
      course: "N3 Japanese",
      enrollmentDate: "2023-12-20",
      status: "completed",
      paidAmount: 1500,
      remainingAmount: 0,
      profileImage: "/images/student.png",
    },
    {
      id: "4",
      name: "Sakura Kimura",
      email: "<EMAIL>",
      phone: "+81-90-4567-8901",
      course: "N5 Japanese",
      enrollmentDate: "2024-01-20",
      status: "active",
      paidAmount: 500,
      remainingAmount: 500,
      profileImage: "/images/student.png",
    },
    {
      id: "5",
      name: "Takeshi Nakamura",
      email: "<EMAIL>",
      phone: "+81-90-5678-9012",
      course: "N4 Japanese",
      enrollmentDate: "2024-01-05",
      status: "suspended",
      paidAmount: 300,
      remainingAmount: 900,
      profileImage: "/images/student.png",
    },
    {
      id: "6",
      name: "Mei Chen",
      email: "<EMAIL>",
      phone: "+86-138-0013-8000",
      course: "N5 Japanese",
      enrollmentDate: "2024-01-18",
      status: "active",
      paidAmount: 1000,
      remainingAmount: 0,
      profileImage: "/images/student.png",
    },
    {
      id: "7",
      name: "Ravi Patel",
      email: "<EMAIL>",
      phone: "+91-98765-43210",
      course: "N4 Japanese",
      enrollmentDate: "2024-01-12",
      status: "active",
      paidAmount: 800,
      remainingAmount: 400,
      profileImage: "/images/student.png",
    },
    {
      id: "8",
      name: "Emma Johnson",
      email: "<EMAIL>",
      phone: "******-123-4567",
      course: "N3 Japanese",
      enrollmentDate: "2023-12-15",
      status: "active",
      paidAmount: 1500,
      remainingAmount: 0,
      profileImage: "/images/student.png",
    },
    {
      id: "9",
      name: "Carlos Rodriguez",
      email: "<EMAIL>",
      phone: "+34-612-345-678",
      course: "N5 Japanese",
      enrollmentDate: "2024-01-22",
      status: "inactive",
      paidAmount: 200,
      remainingAmount: 800,
      profileImage: "/images/student.png",
    },
    {
      id: "10",
      name: "Sophie Martin",
      email: "<EMAIL>",
      phone: "+33-6-12-34-56-78",
      course: "N4 Japanese",
      enrollmentDate: "2024-01-08",
      status: "active",
      paidAmount: 1200,
      remainingAmount: 0,
      profileImage: "/images/student.png",
    },
  ]

  const filteredStudents = students.filter(
    (student) =>
      student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.course.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.phone.includes(searchTerm),
  )

  const stats = {
    totalStudents: students.length,
    activeStudents: students.filter((s) => s.status === "active").length,
    completedStudents: students.filter((s) => s.status === "completed").length,
    suspendedStudents: students.filter((s) => s.status === "suspended").length,
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-white text-black"
      case "completed":
        return "bg-white text-black"
      case "inactive":
      case "suspended":
        return "bg-gray-300 text-black"
      default:
        return "bg-gray-300 text-black"
    }
  }

  return (
    <UserDashboardLayout>
      <div className="min-h-screen bg-black">
        {/* Header */}
        <div className="border-b border-gray-300 bg-black">
          <div className="flex items-center justify-between px-6 py-6">
            <div>
              <h1 className="text-3xl font-bold text-white">Students Management</h1>
              <p className="text-gray-300 mt-1">Manage and track student progress</p>
            </div>
          </div>
        </div>

        <div className="p-8 bg-black min-h-screen">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card className="border-gray-300 rounded-2xl shadow-sm hover:shadow-md transition-shadow bg-black">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-white">Total Students</CardTitle>
                <div className="w-10 h-10 bg-white rounded-xl flex items-center justify-center">
                  <Users className="h-5 w-5 text-black" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-white mb-1">{stats.totalStudents}</div>
                <p className="text-sm text-gray-300">All enrolled students</p>
              </CardContent>
            </Card>

            <Card className="border-gray-300 rounded-2xl shadow-sm hover:shadow-md transition-shadow bg-black">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-white">Active Students</CardTitle>
                <div className="w-10 h-10 bg-white rounded-xl flex items-center justify-center">
                  <GraduationCap className="h-5 w-5 text-black" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-white mb-1">{stats.activeStudents}</div>
                <p className="text-sm text-gray-300">Currently learning</p>
              </CardContent>
            </Card>

            <Card className="border-gray-300 rounded-2xl shadow-sm hover:shadow-md transition-shadow bg-black">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-white">Completed</CardTitle>
                <div className="w-10 h-10 bg-white rounded-xl flex items-center justify-center">
                  <Calendar className="h-5 w-5 text-black" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-white mb-1">{stats.completedStudents}</div>
                <p className="text-sm text-gray-300">Finished courses</p>
              </CardContent>
            </Card>

            <Card className="border-gray-300 rounded-2xl shadow-sm hover:shadow-md transition-shadow bg-black">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-sm font-semibold text-white">Suspended</CardTitle>
                <div className="w-10 h-10 bg-white rounded-xl flex items-center justify-center">
                  <Users className="h-5 w-5 text-black" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-white mb-1">{stats.suspendedStudents}</div>
                <p className="text-sm text-gray-300">Need attention</p>
              </CardContent>
            </Card>
          </div>

          {/* Search and Filter */}
          <div className="flex items-center justify-between mb-6">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-300 h-5 w-5" />
              <Input
                placeholder="Search students..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-12 pr-4 py-3 border-gray-300 focus:border-white rounded-2xl bg-black text-white placeholder:text-gray-300"
              />
            </div>
            <Button
              variant="outline"
              className="border-gray-300 text-white hover:bg-gray-800 bg-black rounded-2xl px-6 py-3"
            >
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
          </div>

          {/* Students List */}
          <Card className="border-gray-300 rounded-2xl shadow-sm bg-black">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-bold text-white">Students ({filteredStudents.length})</CardTitle>
              <CardDescription className="text-gray-300">Manage your students and track their progress</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredStudents.map((student) => (
                  <div
                    key={student.id}
                    className="flex items-center justify-between p-5 border border-gray-300 rounded-2xl hover:bg-gray-800 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 rounded-2xl overflow-hidden bg-white flex items-center justify-center">
                        <img
                          src={student.profileImage || "/images/student.png"}
                          alt={student.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement
                            target.src = "/images/student.png"
                          }}
                        />
                      </div>
                      <div>
                        <p className="font-semibold text-white text-lg">{student.name}</p>
                        <div className="flex items-center space-x-4 mt-1">
                          <div className="flex items-center space-x-1">
                            <Mail className="w-3 h-3 text-gray-300" />
                            <p className="text-sm text-gray-300">{student.email}</p>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Phone className="w-3 h-3 text-gray-300" />
                            <p className="text-sm text-gray-300">{student.phone}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4 mt-1">
                          <div className="flex items-center space-x-1">
                            <GraduationCap className="w-3 h-3 text-gray-300" />
                            <p className="text-sm text-gray-300">{student.course}</p>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="w-3 h-3 text-gray-300" />
                            <p className="text-sm text-gray-300">Enrolled: {student.enrollmentDate}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-6">
                      <div className="text-right">
                        <p className="text-sm font-semibold text-white">Paid: ${student.paidAmount.toLocaleString()}</p>
                        <p className="text-sm text-gray-300">
                          {student.remainingAmount > 0
                            ? `Remaining: $${student.remainingAmount.toLocaleString()}`
                            : "Fully Paid"}
                        </p>
                      </div>
                      <Badge className={`${getStatusColor(student.status)} rounded-xl px-3 py-1 capitalize`}>
                        {student.status}
                      </Badge>
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-gray-300 hover:text-white hover:bg-gray-800 rounded-xl p-2"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-gray-300 hover:text-white hover:bg-gray-800 rounded-xl p-2"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-gray-300 hover:text-white hover:bg-gray-800 rounded-xl p-2"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </UserDashboardLayout>
  )
}

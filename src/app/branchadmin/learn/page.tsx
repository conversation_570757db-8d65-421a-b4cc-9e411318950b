"use client"

import UserDashboardLayout from "@/layouts/user-dashboard-layout"
import { useState } from "react"
import { Search, Plus, BookOpen, Brain, HelpCircle, FileText, Star, Globe, MoreVertical, Eye, Edit } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

interface LearningContent {
  id: number
  type: "alphabet" | "flashcard" | "quiz" | "model_question"
  title: string
  description: string
  is_premium: boolean
  difficulty_level: "beginner" | "intermediate" | "advanced"
  language: string
  order_index: number
  status: "active" | "inactive"
  created_at: string
}

export default function LearningContentManagement() {
  const [searchTerm, setSearchTerm] = useState("")
  const [content] = useState<LearningContent[]>([
    {
      id: 1,
      type: "alphabet",
      title: "Hiragana Basics",
      description: "Learn the fundamental hiragana characters with pronunciation",
      is_premium: false,
      difficulty_level: "beginner",
      language: "en",
      order_index: 1,
      status: "active",
      created_at: "2024-01-15",
    },
    {
      id: 2,
      type: "flashcard",
      title: "N5 Vocabulary Cards",
      description: "Essential vocabulary for JLPT N5 level with audio",
      is_premium: true,
      difficulty_level: "beginner",
      language: "en",
      order_index: 2,
      status: "active",
      created_at: "2024-01-16",
    },
    {
      id: 3,
      type: "quiz",
      title: "Grammar Pattern Quiz",
      description: "Interactive quiz on basic Japanese grammar patterns",
      is_premium: false,
      difficulty_level: "intermediate",
      language: "en",
      order_index: 3,
      status: "active",
      created_at: "2024-01-17",
    },
    {
      id: 4,
      type: "model_question",
      title: "JLPT N4 Practice Test",
      description: "Complete practice test with model questions for N4 level",
      is_premium: true,
      difficulty_level: "intermediate",
      language: "en",
      order_index: 4,
      status: "inactive",
      created_at: "2024-01-18",
    },
  ])

  const filteredContent = content.filter(
    (item) =>
      item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.difficulty_level.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.status.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const totalContent = content.length
  const activeContent = content.filter((c) => c.status === "active").length
  const premiumContent = content.filter((c) => c.is_premium).length
  const quizContent = content.filter((c) => c.type === "quiz" || c.type === "model_question").length

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "alphabet":
        return BookOpen
      case "flashcard":
        return Brain
      case "quiz":
        return HelpCircle
      case "model_question":
        return FileText
      default:
        return BookOpen
    }
  }

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case "beginner":
        return "bg-white text-black"
      case "intermediate":
        return "bg-white text-black"
      case "advanced":
        return "bg-gray-300 text-black"
      default:
        return "bg-gray-300 text-black"
    }
  }

  return (
    <UserDashboardLayout>
      <div className="flex flex-1 flex-col gap-6 p-6 bg-black text-white">
        {/* Header */}
        <div className="flex flex-col gap-2 pb-6 border-b border-gray-300">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white">Learning Content Update</h1>
              <p className="text-gray-300 mt-1">Manage educational content, quizzes, and learning materials</p>
            </div>
            <Button className="bg-white text-black hover:bg-gray-300 rounded-2xl px-6">
              <Plus className="w-4 h-4 mr-2" />
              Add Content
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm font-medium">Total Content</p>
                <p className="text-3xl font-bold text-black mt-1">{totalContent}</p>
              </div>
              <div className="bg-black rounded-xl p-3">
                <BookOpen className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm font-medium">Active Content</p>
                <p className="text-3xl font-bold text-black mt-1">{activeContent}</p>
              </div>
              <div className="bg-black rounded-xl p-3">
                <Brain className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm font-medium">Premium Content</p>
                <p className="text-3xl font-bold text-black mt-1">{premiumContent}</p>
              </div>
              <div className="bg-black rounded-xl p-3">
                <Star className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm font-medium">Quizzes & Tests</p>
                <p className="text-3xl font-bold text-black mt-1">{quizContent}</p>
              </div>
              <div className="bg-black rounded-xl p-3">
                <HelpCircle className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Search */}
        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-300 w-4 h-4" />
            <Input
              placeholder="Search content..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-white text-black border-gray-300 rounded-2xl focus:ring-2 focus:ring-gray-300"
            />
          </div>
        </div>

        {/* Content List */}
        <div className="bg-white rounded-2xl p-6 border border-gray-300">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-black">Learning Content ({filteredContent.length})</h2>
            <p className="text-gray-300 text-sm">Manage your educational materials</p>
          </div>

          <div className="space-y-4">
            {filteredContent.map((item) => {
              const IconComponent = getTypeIcon(item.type)
              return (
                <div
                  key={item.id}
                  className="bg-black rounded-2xl p-6 border border-gray-300 hover:shadow-md transition-all duration-200"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="bg-white rounded-xl p-3">
                        <IconComponent className="w-6 h-6 text-black" />
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold text-white text-lg">{item.title}</h3>
                          {item.is_premium && <Star className="w-4 h-4 text-white fill-current" />}
                        </div>
                        <p className="text-gray-300 text-sm mt-1">{item.description}</p>
                        <div className="flex items-center gap-4 mt-2">
                          <div className="flex items-center gap-1 text-gray-300 text-sm">
                            <span className="capitalize">{item.type.replace("_", " ")}</span>
                          </div>
                          <div className="flex items-center gap-1 text-gray-300 text-sm">
                            <Globe className="w-4 h-4" />
                            {item.language.toUpperCase()}
                          </div>
                          <div className="text-gray-300 text-sm">Order: {item.order_index}</div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-6">
                      <div className="text-right">
                        <div className="flex items-center gap-2 mb-2">
                          <span
                            className={`px-3 py-1 rounded-full text-xs font-medium ${getDifficultyColor(item.difficulty_level)}`}
                          >
                            {item.difficulty_level}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span
                            className={`px-3 py-1 rounded-full text-xs font-medium ${
                              item.status === "active" ? "bg-white text-black" : "bg-gray-300 text-black"
                            }`}
                          >
                            {item.status}
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-gray-300 hover:text-white hover:bg-gray-300/10 rounded-xl"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-gray-300 hover:text-white hover:bg-gray-300/10 rounded-xl"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-gray-300 hover:text-white hover:bg-gray-300/10 rounded-xl"
                        >
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </UserDashboardLayout>
  )
}

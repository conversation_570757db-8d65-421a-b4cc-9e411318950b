import UserDashboardLayout from "@/layouts/user-dashboard-layout";
import { useState } from "react";
import { Search, Plus, Building2, Phone, Link, Users, Calendar, MoreVertical, Eye, Edit, Trash2 } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface Institute {
  id: number;
  name: string;
  phone: string;
  referral_link: string;
  status: 'active' | 'disabled';
  created_at: string;
  student_count: number;
  course_count: number;
}

export default function InstituteManagement() {
  const [searchTerm, setSearchTerm] = useState("");
  const [institutes] = useState<Institute[]>([
    {
      id: 1,
      name: "Tokyo Language Institute",
      phone: "+81-3-1234-5678",
      referral_link: "https://app.com/ref/tokyo-lang-inst",
      status: "active",
      created_at: "2024-01-15",
      student_count: 245,
      course_count: 8
    },
    {
      id: 2,
      name: "Osaka Japanese Academy",
      phone: "+81-6-9876-5432",
      referral_link: "https://app.com/ref/osaka-jp-academy",
      status: "active",
      created_at: "2024-02-20",
      student_count: 189,
      course_count: 6
    },
    {
      id: 3,
      name: "Kyoto Cultural Center",
      phone: "+81-75-5555-1234",
      referral_link: "https://app.com/ref/kyoto-cultural",
      status: "disabled",
      created_at: "2024-03-10",
      student_count: 67,
      course_count: 3
    }
  ]);

  const filteredInstitutes = institutes.filter(institute =>
    institute.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    institute.phone.includes(searchTerm) ||
    institute.status.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalInstitutes = institutes.length;
  const activeInstitutes = institutes.filter(i => i.status === 'active').length;
  const totalStudents = institutes.reduce((sum, i) => sum + i.student_count, 0);
  const totalCourses = institutes.reduce((sum, i) => sum + i.course_count, 0);

  return (
    <UserDashboardLayout>
      <div className="flex flex-1 flex-col gap-6 p-6 bg-black text-white">
        {/* Header */}
        <div className="flex flex-col gap-2 pb-6 border-b border-gray-300">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white">Institute Management</h1>
              <p className="text-gray-300 mt-1">Manage and oversee all registered institutes</p>
            </div>
            <Button className="bg-white text-black hover:bg-gray-300 rounded-2xl px-6">
              <Plus className="w-4 h-4 mr-2" />
              Add Institute
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm font-medium">Total Institutes</p>
                <p className="text-3xl font-bold text-black mt-1">{totalInstitutes}</p>
              </div>
              <div className="bg-black rounded-xl p-3">
                <Building2 className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm font-medium">Active Institutes</p>
                <p className="text-3xl font-bold text-black mt-1">{activeInstitutes}</p>
              </div>
              <div className="bg-black rounded-xl p-3">
                <Users className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm font-medium">Total Students</p>
                <p className="text-3xl font-bold text-black mt-1">{totalStudents}</p>
              </div>
              <div className="bg-black rounded-xl p-3">
                <Users className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm font-medium">Total Courses</p>
                <p className="text-3xl font-bold text-black mt-1">{totalCourses}</p>
              </div>
              <div className="bg-black rounded-xl p-3">
                <Calendar className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Search */}
        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-300 w-4 h-4" />
            <Input
              placeholder="Search institutes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-white text-black border-gray-300 rounded-2xl focus:ring-2 focus:ring-gray-300"
            />
          </div>
        </div>

        {/* Institutes List */}
        <div className="bg-white rounded-2xl p-6 border border-gray-300">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-black">
              Institutes ({filteredInstitutes.length})
            </h2>
            <p className="text-gray-300 text-sm">Manage your registered institutes</p>
          </div>

          <div className="space-y-4">
            {filteredInstitutes.map((institute) => (
              <div
                key={institute.id}
                className="bg-black rounded-2xl p-6 border border-gray-300 hover:shadow-md transition-all duration-200"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="bg-white rounded-xl p-3">
                      <Building2 className="w-6 h-6 text-black" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-white text-lg">{institute.name}</h3>
                      <div className="flex items-center gap-4 mt-1">
                        <div className="flex items-center gap-1 text-gray-300 text-sm">
                          <Phone className="w-4 h-4" />
                          {institute.phone}
                        </div>
                        <div className="flex items-center gap-1 text-gray-300 text-sm">
                          <Link className="w-4 h-4" />
                          Referral Link
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-6">
                    <div className="text-right">
                      <div className="flex items-center gap-4 text-sm text-gray-300">
                        <span>{institute.student_count} students</span>
                        <span>{institute.course_count} courses</span>
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        <span
                          className={`px-3 py-1 rounded-full text-xs font-medium ${
                            institute.status === 'active'
                              ? 'bg-white text-black'
                              : 'bg-gray-300 text-black'
                          }`}
                        >
                          {institute.status}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="sm" className="text-gray-300 hover:text-white hover:bg-gray-300/10 rounded-xl">
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm" className="text-gray-300 hover:text-white hover:bg-gray-300/10 rounded-xl">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm" className="text-gray-300 hover:text-white hover:bg-gray-300/10 rounded-xl">
                        <MoreVertical className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </UserDashboardLayout>
  );
}

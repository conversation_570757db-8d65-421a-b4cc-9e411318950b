"use client"

import { useState } from "react"
import UserDashboardLayout from "@/layouts/user-dashboard-layout"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Users,
  Calendar,
  TrendingUp,
  FileText,
  Search,
  Filter,
  MoreVertical,
  Eye,
  Edit,
  Trash2,
  Plus,
} from "lucide-react"

interface Consultant {
  id: string
  name: string
  email: string
  specialization: string
  status: "active" | "inactive"
  clients: number
  revenue: number
}

interface Appointment {
  id: string
  clientName: string
  consultantName: string
  date: string
  time: string
  status: "scheduled" | "completed" | "cancelled"
}

export default function BranchAdminDashboard() {
  const [searchTerm, setSearchTerm] = useState("")
  const [activeTab, setActiveTab] = useState("overview")

  // Mock data
  const consultants: Consultant[] = [
    {
      id: "1",
      name: "<PERSON><PERSON> <PERSON>",
      email: "<EMAIL>",
      specialization: "Business Strategy",
      status: "active",
      clients: 15,
      revenue: 45000,
    },
    {
      id: "2",
      name: "<PERSON>",
      email: "<EMAIL>",
      specialization: "Financial Planning",
      status: "active",
      clients: 12,
      revenue: 38000,
    },
    {
      id: "3",
      name: "Emily Rodriguez",
      email: "<EMAIL>",
      specialization: "HR Consulting",
      status: "inactive",
      clients: 8,
      revenue: 22000,
    },
  ]

  const appointments: Appointment[] = [
    {
      id: "1",
      clientName: "ABC Corp",
      consultantName: "Dr. Sarah Johnson",
      date: "2024-01-15",
      time: "10:00 AM",
      status: "scheduled",
    },
    {
      id: "2",
      clientName: "XYZ Ltd",
      consultantName: "Michael Chen",
      date: "2024-01-15",
      time: "2:00 PM",
      status: "completed",
    },
    {
      id: "3",
      clientName: "Tech Solutions",
      consultantName: "Emily Rodriguez",
      date: "2024-01-16",
      time: "11:00 AM",
      status: "cancelled",
    },
  ]

  const stats = {
    totalConsultants: consultants.length,
    activeConsultants: consultants.filter((c) => c.status === "active").length,
    totalRevenue: consultants.reduce((sum, c) => sum + c.revenue, 0),
    totalClients: consultants.reduce((sum, c) => sum + c.clients, 0),
  }

  const filteredConsultants = consultants.filter(
    (consultant) =>
      consultant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      consultant.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      consultant.specialization.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
      case "scheduled":
      case "completed":
        return "bg-white text-black"
      case "inactive":
      case "cancelled":
        return "bg-white text-black"
      default:
        return "bg-white text-black"
    }
  }

  return (
    <UserDashboardLayout>
      <div className="min-h-screen bg-black">


        <div className="p-8 bg-black min-h-screen">
          {activeTab === "overview" && (
            <div className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card className="border-white rounded-2xl shadow-sm hover:shadow-md transition-shadow bg-black">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                    <CardTitle className="text-sm font-semibold text-white">Total Consultants</CardTitle>
                    <div className="w-10 h-10 bg-white rounded-xl flex items-center justify-center">
                      <Users className="h-5 w-5 text-black" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-white mb-1">{stats.totalConsultants}</div>
                    <p className="text-sm text-white">{stats.activeConsultants} active</p>
                  </CardContent>
                </Card>

                <Card className="border-white rounded-2xl shadow-sm hover:shadow-md transition-shadow bg-black">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                    <CardTitle className="text-sm font-semibold text-white">Total Revenue</CardTitle>
                    <div className="w-10 h-10 bg-white rounded-xl flex items-center justify-center">
                      <TrendingUp className="h-5 w-5 text-black" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-white mb-1">${stats.totalRevenue.toLocaleString()}</div>
                    <p className="text-sm text-white">This quarter</p>
                  </CardContent>
                </Card>

                <Card className="border-white rounded-2xl shadow-sm hover:shadow-md transition-shadow bg-black">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                    <CardTitle className="text-sm font-semibold text-white">Active Clients</CardTitle>
                    <div className="w-10 h-10 bg-white rounded-xl flex items-center justify-center">
                      <Users className="h-5 w-5 text-black" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-white mb-1">{stats.totalClients}</div>
                    <p className="text-sm text-white">Across all consultants</p>
                  </CardContent>
                </Card>

                <Card className="border-white rounded-2xl shadow-sm hover:shadow-md transition-shadow bg-black">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                    <CardTitle className="text-sm font-semibold text-white">Appointments</CardTitle>
                    <div className="w-10 h-10 bg-white rounded-xl flex items-center justify-center">
                      <Calendar className="h-5 w-5 text-black" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-white mb-1">{appointments.length}</div>
                    <p className="text-sm text-white">This week</p>
                  </CardContent>
                </Card>
              </div>

              <Card className="border-white rounded-2xl shadow-sm bg-black">
                <CardHeader className="pb-4">
                  <CardTitle className="text-xl font-bold text-white">Recent Appointments</CardTitle>
                  <CardDescription className="text-white">Latest scheduled consultations</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {appointments.slice(0, 3).map((appointment) => (
                      <div
                        key={appointment.id}
                        className="flex items-center justify-between p-5 border border-white rounded-2xl hover:bg-black transition-colors"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="w-3 h-3 bg-white rounded-full"></div>
                          <div>
                            <p className="font-semibold text-white">{appointment.clientName}</p>
                            <p className="text-sm text-white">{appointment.consultantName}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          <div className="text-right">
                            <p className="text-sm font-semibold text-white">{appointment.date}</p>
                            <p className="text-sm text-white">{appointment.time}</p>
                          </div>
                          <Badge className={`${getStatusColor(appointment.status)} rounded-xl px-3 py-1`}>
                            {appointment.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === "consultants" && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white h-5 w-5" />
                  <Input
                    placeholder="Search consultants..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-12 pr-4 py-3 border-white focus:border-white rounded-2xl bg-black text-white placeholder:text-white"
                  />
                </div>
                <Button
                  variant="outline"
                  className="border-white text-white hover:bg-black bg-black rounded-2xl px-6 py-3"
                >
                  <Filter className="w-4 h-4 mr-2" />
                  Filter
                </Button>
              </div>

              <Card className="border-white rounded-2xl shadow-sm bg-black">
                <CardHeader className="pb-4">
                  <CardTitle className="text-xl font-bold text-white">
                    Consultants ({filteredConsultants.length})
                  </CardTitle>
                  <CardDescription className="text-white">Manage your branch consultants</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {filteredConsultants.map((consultant) => (
                      <div
                        key={consultant.id}
                        className="flex items-center justify-between p-5 border border-white rounded-2xl hover:bg-black transition-colors"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 bg-white rounded-2xl flex items-center justify-center">
                            <span className="text-black font-semibold text-sm">
                              {consultant.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </span>
                          </div>
                          <div>
                            <p className="font-semibold text-white">{consultant.name}</p>
                            <p className="text-sm text-white">{consultant.email}</p>
                            <p className="text-sm text-white">{consultant.specialization}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-6">
                          <div className="text-right">
                            <p className="text-sm font-semibold text-white">{consultant.clients} clients</p>
                            <p className="text-sm text-white">${consultant.revenue.toLocaleString()}</p>
                          </div>
                          <Badge className={`${getStatusColor(consultant.status)} rounded-xl px-3 py-1`}>
                            {consultant.status}
                          </Badge>
                          <div className="flex items-center space-x-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-white hover:text-white hover:bg-black rounded-xl p-2"
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-white hover:text-white hover:bg-black rounded-xl p-2"
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-white hover:text-white hover:bg-black rounded-xl p-2"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === "appointments" && (
            <Card className="border-white rounded-2xl shadow-sm bg-black">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-bold text-white">All Appointments</CardTitle>
                <CardDescription className="text-white">View and manage scheduled consultations</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {appointments.map((appointment) => (
                    <div
                      key={appointment.id}
                      className="flex items-center justify-between p-5 border border-white rounded-2xl hover:bg-black transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-white rounded-xl flex items-center justify-center">
                          <Calendar className="w-5 h-5 text-black" />
                        </div>
                        <div>
                          <p className="font-semibold text-white">{appointment.clientName}</p>
                          <p className="text-sm text-white">with {appointment.consultantName}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <p className="text-sm font-semibold text-white">{appointment.date}</p>
                          <p className="text-sm text-white">{appointment.time}</p>
                        </div>
                        <Badge className={`${getStatusColor(appointment.status)} rounded-xl px-3 py-1`}>
                          {appointment.status}
                        </Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-white hover:text-white hover:bg-black rounded-xl p-2"
                        >
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {activeTab === "reports" && (
            <div className="space-y-6">
              <Card className="border-white rounded-2xl shadow-sm bg-black">
                <CardHeader className="pb-4">
                  <CardTitle className="text-xl font-bold text-white">Branch Performance Reports</CardTitle>
                  <CardDescription className="text-white">Generate and view performance analytics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Button
                      variant="outline"
                      className="h-32 border-white text-white hover:bg-black bg-black rounded-2xl p-6 transition-all hover:shadow-md"
                    >
                      <div className="text-center">
                        <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center mx-auto mb-3">
                          <FileText className="w-6 h-6 text-black" />
                        </div>
                        <p className="font-semibold text-lg">Revenue Report</p>
                        <p className="text-sm text-white mt-1">Monthly revenue analysis</p>
                      </div>
                    </Button>
                    <Button
                      variant="outline"
                      className="h-32 border-white text-white hover:bg-black bg-black rounded-2xl p-6 transition-all hover:shadow-md"
                    >
                      <div className="text-center">
                        <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center mx-auto mb-3">
                          <Users className="w-6 h-6 text-black" />
                        </div>
                        <p className="font-semibold text-lg">Consultant Performance</p>
                        <p className="text-sm text-white mt-1">Individual metrics</p>
                      </div>
                    </Button>
                    <Button
                      variant="outline"
                      className="h-32 border-white text-white hover:bg-black bg-black rounded-2xl p-6 transition-all hover:shadow-md"
                    >
                      <div className="text-center">
                        <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center mx-auto mb-3">
                          <TrendingUp className="w-6 h-6 text-black" />
                        </div>
                        <p className="font-semibold text-lg">Growth Analytics</p>
                        <p className="text-sm text-white mt-1">Quarterly trends</p>
                      </div>
                    </Button>
                    <Button
                      variant="outline"
                      className="h-32 border-white text-white hover:bg-black bg-black rounded-2xl p-6 transition-all hover:shadow-md"
                    >
                      <div className="text-center">
                        <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center mx-auto mb-3">
                          <Calendar className="w-6 h-6 text-black" />
                        </div>
                        <p className="font-semibold text-lg">Appointment Summary</p>
                        <p className="text-sm text-white mt-1">Booking statistics</p>
                      </div>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </UserDashboardLayout>
  )
}

"use client"

import { useState } from "react"
import { Search, Eye, Edit, Trash2, Phone, Mail, MapPin } from 'lucide-react'
import UserDashboardLayout from "@/layouts/user-dashboard-layout"

export default function StaffList() {
  const [searchTerm, setSearchTerm] = useState("")

  const staffMembers = [
    {
      id: 1,
      name: "Dr. <PERSON>",
      email: "<EMAIL>",
      phone: "****** 567 8901",
      position: "Senior Instructor",
      department: "Japanese Language",
      salary: "$65,000",
      status: "active",
      joinDate: "2023-01-15"
    },
    {
      id: 2,
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "****** 567 8902",
      position: "Assistant Professor",
      department: "Business Japanese",
      salary: "$55,000",
      status: "active",
      joinDate: "2023-03-20"
    },
    {
      id: 3,
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "****** 567 8903",
      position: "Language Coordinator",
      department: "JLPT Preparation",
      salary: "$50,000",
      status: "inactive",
      joinDate: "2022-09-10"
    }
  ]

  const filteredStaff = staffMembers.filter(staff =>
    staff.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    staff.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    staff.position.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const activeStaff = staffMembers.filter(staff => staff.status === "active").length
  const totalStaff = staffMembers.length

  return (
    <UserDashboardLayout>
      <div className="p-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Staff Management</h1>
          <p className="text-gray-300">Manage your branch staff members</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-black border border-gray-300 rounded-2xl p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm">Total Staff</p>
                <p className="text-3xl font-bold text-white">{totalStaff}</p>
              </div>
            </div>
          </div>
          <div className="bg-black border border-gray-300 rounded-2xl p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm">Active Staff</p>
                <p className="text-3xl font-bold text-white">{activeStaff}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-black border border-gray-300 rounded-2xl p-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white">Staff Members ({filteredStaff.length})</h2>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-300 h-4 w-4" />
              <input
                type="text"
                placeholder="Search staff..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 bg-black border border-gray-300 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:border-white w-80"
              />
            </div>
          </div>

          <div className="space-y-4">
            {filteredStaff.map((staff) => (
              <div key={staff.id} className="bg-black border border-gray-300 rounded-2xl p-6 hover:border-white transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                      <span className="text-black font-bold text-lg">
                        {staff.name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white">{staff.name}</h3>
                      <p className="text-gray-300">{staff.position}</p>
                      <div className="flex items-center space-x-4 mt-2">
                        <div className="flex items-center space-x-1">
                          <Mail className="h-4 w-4 text-gray-300" />
                          <span className="text-sm text-gray-300">{staff.email}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Phone className="h-4 w-4 text-gray-300" />
                          <span className="text-sm text-gray-300">{staff.phone}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="text-white font-semibold">{staff.salary}</p>
                      <p className="text-gray-300 text-sm">{staff.department}</p>
                      <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium mt-2 ${
                        staff.status === 'active' 
                          ? 'bg-white text-black' 
                          : 'bg-gray-300 text-black'
                      }`}>
                        {staff.status}
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      <button className="p-2 bg-black border border-gray-300 rounded-lg hover:bg-white hover:text-black transition-colors">
                        <Eye className="h-4 w-4" />
                      </button>
                      <button className="p-2 bg-black border border-gray-300 rounded-lg hover:bg-white hover:text-black transition-colors">
                        <Edit className="h-4 w-4" />
                      </button>
                      <button className="p-2 bg-black border border-gray-300 rounded-lg hover:bg-white hover:text-black transition-colors">
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </UserDashboardLayout>
  )
}

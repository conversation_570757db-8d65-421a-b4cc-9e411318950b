"use client"

import UserDashboardLayout from "@/layouts/user-dashboard-layout"
import { useState } from "react"
import { Search, Plus, Calendar, Clock, Users, Video, User, MoreVertical, Eye, Edit } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

interface OnlineClass {
  id: number
  title: string
  description: string
  class_date: string
  start_time: string
  end_time: string
  google_meet_link: string
  instructor_name: string
  course_name: string
  max_participants: number
  registered_count: number
  status: "scheduled" | "ongoing" | "completed" | "cancelled"
}

export default function OnlineClassManagement() {
  const [searchTerm, setSearchTerm] = useState("")
  const [classes] = useState<OnlineClass[]>([
    {
      id: 1,
      title: "N5 Grammar Fundamentals",
      description: "Basic Japanese grammar patterns for beginners",
      class_date: "2024-01-20",
      start_time: "10:00",
      end_time: "11:30",
      google_meet_link: "https://meet.google.com/abc-defg-hij",
      instructor_name: "<PERSON>. <PERSON>",
      course_name: "JLPT N5 Preparation",
      max_participants: 30,
      registered_count: 25,
      status: "scheduled",
    },
    {
      id: 2,
      title: "Kanji Writing Practice",
      description: "Interactive kanji writing session with stroke order",
      class_date: "2024-01-21",
      start_time: "14:00",
      end_time: "15:00",
      google_meet_link: "https://meet.google.com/xyz-uvwx-yz",
      instructor_name: "Michael Chen",
      course_name: "Japanese Writing Skills",
      max_participants: 20,
      registered_count: 18,
      status: "ongoing",
    },
    {
      id: 3,
      title: "Business Japanese Conversation",
      description: "Professional Japanese communication skills",
      class_date: "2024-01-19",
      start_time: "16:00",
      end_time: "17:30",
      google_meet_link: "https://meet.google.com/bus-jpn-conv",
      instructor_name: "Emily Rodriguez",
      course_name: "Business Japanese",
      max_participants: 15,
      registered_count: 12,
      status: "completed",
    },
  ])

  const filteredClasses = classes.filter(
    (cls) =>
      cls.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cls.instructor_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cls.course_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cls.status.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const totalClasses = classes.length
  const scheduledClasses = classes.filter((c) => c.status === "scheduled").length
  const ongoingClasses = classes.filter((c) => c.status === "ongoing").length
  const completedClasses = classes.filter((c) => c.status === "completed").length

  const getStatusColor = (status: string) => {
    switch (status) {
      case "scheduled":
        return "bg-white text-black"
      case "ongoing":
        return "bg-white text-black"
      case "completed":
        return "bg-gray-300 text-black"
      case "cancelled":
        return "bg-gray-300 text-black"
      default:
        return "bg-gray-300 text-black"
    }
  }

  return (
    <UserDashboardLayout>
      <div className="flex flex-1 flex-col gap-6 p-6 bg-black text-white">
        {/* Header */}
        <div className="flex flex-col gap-2 pb-6 border-b border-gray-300">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white">Online Class Scheduling</h1>
              <p className="text-gray-300 mt-1">Manage virtual classes and Google Meet sessions</p>
            </div>
            <Button className="bg-white text-black hover:bg-gray-300 rounded-2xl px-6">
              <Plus className="w-4 h-4 mr-2" />
              Schedule Class
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm font-medium">Total Classes</p>
                <p className="text-3xl font-bold text-black mt-1">{totalClasses}</p>
              </div>
              <div className="bg-black rounded-xl p-3">
                <Calendar className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm font-medium">Scheduled</p>
                <p className="text-3xl font-bold text-black mt-1">{scheduledClasses}</p>
              </div>
              <div className="bg-black rounded-xl p-3">
                <Clock className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm font-medium">Ongoing</p>
                <p className="text-3xl font-bold text-black mt-1">{ongoingClasses}</p>
              </div>
              <div className="bg-black rounded-xl p-3">
                <Video className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm font-medium">Completed</p>
                <p className="text-3xl font-bold text-black mt-1">{completedClasses}</p>
              </div>
              <div className="bg-black rounded-xl p-3">
                <Users className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>
        </div>

        {/* Search */}
        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-300 w-4 h-4" />
            <Input
              placeholder="Search classes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-white text-black border-gray-300 rounded-2xl focus:ring-2 focus:ring-gray-300"
            />
          </div>
        </div>

        {/* Classes List */}
        <div className="bg-white rounded-2xl p-6 border border-gray-300">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-black">Classes ({filteredClasses.length})</h2>
            <p className="text-gray-300 text-sm">Manage your online classes</p>
          </div>

          <div className="space-y-4">
            {filteredClasses.map((cls) => (
              <div
                key={cls.id}
                className="bg-black rounded-2xl p-6 border border-gray-300 hover:shadow-md transition-all duration-200"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="bg-white rounded-xl p-3">
                      <Video className="w-6 h-6 text-black" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-white text-lg">{cls.title}</h3>
                      <p className="text-gray-300 text-sm mt-1">{cls.description}</p>
                      <div className="flex items-center gap-4 mt-2">
                        <div className="flex items-center gap-1 text-gray-300 text-sm">
                          <Calendar className="w-4 h-4" />
                          {cls.class_date}
                        </div>
                        <div className="flex items-center gap-1 text-gray-300 text-sm">
                          <Clock className="w-4 h-4" />
                          {cls.start_time} - {cls.end_time}
                        </div>
                        <div className="flex items-center gap-1 text-gray-300 text-sm">
                          <User className="w-4 h-4" />
                          {cls.instructor_name}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-6">
                    <div className="text-right">
                      <div className="text-sm text-gray-300">
                        <p>{cls.course_name}</p>
                        <p className="mt-1">
                          {cls.registered_count}/{cls.max_participants} participants
                        </p>
                      </div>
                      <div className="flex items-center gap-2 mt-2">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(cls.status)}`}>
                          {cls.status}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-300 hover:text-white hover:bg-gray-300/10 rounded-xl"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-300 hover:text-white hover:bg-gray-300/10 rounded-xl"
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-300 hover:text-white hover:bg-gray-300/10 rounded-xl"
                      >
                        <MoreVertical className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </UserDashboardLayout>
  )
}

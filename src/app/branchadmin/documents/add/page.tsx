"use client"

import type React from "react"

import { useState } from "react"
import UserDashboardLayout from "@/layouts/user-dashboard-layout"

export default function AddStaff() {
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    position: "",
    department: "",
    salary: "",
    joinDate: "",
    profileImage: null as File | null,
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Adding staff:", formData)
  }

  return (
    <UserDashboardLayout>
      <div className="p-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Add New Staff Member</h1>
          <p className="text-gray-300">Add a new staff member to your branch</p>
        </div>

        <div className="bg-black border border-gray-300 rounded-2xl p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-white text-sm font-medium mb-2">First Name</label>
                <input
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                  className="w-full px-4 py-3 bg-black border border-gray-300 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:border-white"
                  placeholder="Enter first name"
                  required
                />
              </div>
              <div>
                <label className="block text-white text-sm font-medium mb-2">Last Name</label>
                <input
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                  className="w-full px-4 py-3 bg-black border border-gray-300 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:border-white"
                  placeholder="Enter last name"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-white text-sm font-medium mb-2">Position</label>
                <select
                  value={formData.position}
                  onChange={(e) => setFormData({ ...formData, position: e.target.value })}
                  className="w-full px-4 py-3 bg-black border border-gray-300 rounded-xl text-white focus:outline-none focus:border-white"
                  required
                >
                  <option value="">Select Position</option>
                  <option value="Senior Instructor">Senior Instructor</option>
                  <option value="Assistant Professor">Assistant Professor</option>
                  <option value="Language Coordinator">Language Coordinator</option>
                  <option value="Administrative Staff">Administrative Staff</option>
                </select>
              </div>
              <div>
                <label className="block text-white text-sm font-medium mb-2">Department</label>
                <select
                  value={formData.department}
                  onChange={(e) => setFormData({ ...formData, department: e.target.value })}
                  className="w-full px-4 py-3 bg-black border border-gray-300 rounded-xl text-white focus:outline-none focus:border-white"
                  required
                >
                  <option value="">Select Department</option>
                  <option value="Japanese Language">Japanese Language</option>
                  <option value="Business Japanese">Business Japanese</option>
                  <option value="JLPT Preparation">JLPT Preparation</option>
                  <option value="Administration">Administration</option>
                </select>
              </div>
            </div>

            <div className="flex gap-4 pt-6">
              <button
                type="submit"
                className="px-8 py-3 bg-white text-black rounded-xl font-medium hover:bg-gray-300 transition-colors"
              >
                Add Staff Member
              </button>
              <button
                type="button"
                className="px-8 py-3 bg-black border border-gray-300 text-white rounded-xl font-medium hover:bg-white hover:text-black transition-colors"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </UserDashboardLayout>
  )
}

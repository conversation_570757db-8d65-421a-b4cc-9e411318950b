import React, { useState } from 'react';
import AdminDashboardLayout from "@/layouts/admin-dashboard-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  FileText, CheckCircle, Clock, XCircle, Eye, Filter,
  Download, Building2, User, Calendar, AlertTriangle, 
  TrendingUp, BarChart3, MoreHorizontal
} from "lucide-react";

// Mock data
const documentData = [
  {
    id: 1,
    institute: "Nihon Institute of Technology",
    studentName: "<PERSON>",
    studentId: "STU-2024-001",
    documentType: "ID Card",
    status: "verified",
    submittedDate: "2024-08-10",
    lastUpdated: "2024-08-16",
    verifiedBy: "Yu<PERSON>",
    verificationNotes: "All documents verified successfully. ID card matches student records.",
    priority: "high",
    processingTime: "6 days"
  },
  {
    id: 2,
    institute: "Osaka Business Academy",
    studentName: "<PERSON> Chen",
    studentId: "STU-2024-002",
    documentType: "Graduation Certificate",
    status: "verified",
    submittedDate: "2024-08-08",
    lastUpdated: "2024-08-15",
    verifiedBy: "Hiroshi Sato",
    verificationNotes: "Certificate verified with issuing institution. All requirements met.",
    priority: "high",
    processingTime: "7 days"
  },
  {
    id: 3,
    institute: "Kyoto Learning Center",
    studentName: "Kenji Suzuki",
    studentId: "STU-2024-003",
    documentType: "Transcript",
    status: "pending",
    submittedDate: "2024-08-12",
    lastUpdated: "2024-08-16",
    verifiedBy: "Akiko Watanabe",
    verificationNotes: "Transcript received but requires additional verification.",
    priority: "medium",
    processingTime: "4 days"
  },
  {
    id: 4,
    institute: "Yokohama Education Institute",
    studentName: "Hanako Sato",
    studentId: "STU-2024-004",
    documentType: "Passport Copy",
    status: "rejected",
    submittedDate: "2024-08-05",
    lastUpdated: "2024-08-14",
    verifiedBy: "Masato Kimura",
    verificationNotes: "Passport copy is unclear and expired. Need valid copy.",
    priority: "high",
    processingTime: "9 days"
  },
  {
    id: 5,
    institute: "Sapporo Technical College",
    studentName: "Takeshi Nakamura",
    studentId: "STU-2024-005",
    documentType: "Medical Certificate",
    status: "verified",
    submittedDate: "2024-08-11",
    lastUpdated: "2024-08-16",
    verifiedBy: "Yumi Nakamura",
    verificationNotes: "Medical certificate verified. All health requirements satisfied.",
    priority: "medium",
    processingTime: "5 days"
  }
];

const statusSummary = {
  total: documentData.length,
  verified: documentData.filter(doc => doc.status === "verified").length,
  pending: documentData.filter(doc => doc.status === "pending").length,
  rejected: documentData.filter(doc => doc.status === "rejected").length
};

export default function DocumentStatus() {
  const [selectedInstitute, setSelectedInstitute] = useState<string>("all");
  const [selectedDocumentType, setSelectedDocumentType] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [selectedDocument, setSelectedDocument] = useState<any>(null);
  const [showModal, setShowModal] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "verified": return "text-green-600 bg-green-50 dark:bg-green-950";
      case "pending": return "text-yellow-600 bg-yellow-50 dark:bg-yellow-950";
      case "rejected": return "text-red-600 bg-red-50 dark:bg-red-950";
      default: return "text-gray-600 bg-gray-50 dark:bg-gray-950";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "verified": return <CheckCircle className="h-4 w-4" />;
      case "pending": return <Clock className="h-4 w-4" />;
      case "rejected": return <XCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-800 dark:bg-red-950 dark:text-red-300";
      case "medium": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-950 dark:text-yellow-300";
      case "low": return "bg-green-100 text-green-800 dark:bg-green-950 dark:text-green-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-950 dark:text-gray-300";
    }
  };

  const filteredDocuments = documentData.filter(doc => {
    const instituteMatch = selectedInstitute === "all" || doc.institute === selectedInstitute;
    const typeMatch = selectedDocumentType === "all" || doc.documentType === selectedDocumentType;
    const statusMatch = selectedStatus === "all" || doc.status === selectedStatus;
    return instituteMatch && typeMatch && statusMatch;
  });

  const handleViewDetails = (document: any) => {
    setSelectedDocument(document);
    setShowModal(true);
  };

  const handleExportCSV = () => {
    console.log("Exporting document status to CSV...");
    alert("Document status exported to CSV successfully!");
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedDocument(null);
  };

  return (
    <AdminDashboardLayout>
      <div className="flex flex-1 flex-col gap-6 p-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Document Status</h1>
            <p className="text-muted-foreground">
              Monitor document verification status across all institutes
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Advanced Filter
            </Button>
            <Button onClick={handleExportCSV} size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </div>

        {/* Status Summary Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Documents</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statusSummary.total}</div>
              <p className="text-xs text-muted-foreground">Across all institutes</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Verified</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{statusSummary.verified}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">
                  {Math.round((statusSummary.verified / statusSummary.total) * 100)}%
                </span> of total
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{statusSummary.pending}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-yellow-600">
                  {Math.round((statusSummary.pending / statusSummary.total) * 100)}%
                </span> of total
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Rejected</CardTitle>
              <XCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{statusSummary.rejected}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-red-600">
                  {Math.round((statusSummary.rejected / statusSummary.total) * 100)}%
                </span> of total
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filter Documents
            </CardTitle>
            <CardDescription>
              Filter documents by institute, type, or status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div>
                <label className="text-sm font-medium mb-2 block">Institute</label>
                <select 
                  value={selectedInstitute}
                  onChange={(e) => setSelectedInstitute(e.target.value)}
                  className="w-full p-2 border rounded-md bg-background"
                >
                  <option value="all">All Institutes</option>
                  <option value="Nihon Institute of Technology">Nihon Institute of Technology</option>
                  <option value="Osaka Business Academy">Osaka Business Academy</option>
                  <option value="Kyoto Learning Center">Kyoto Learning Center</option>
                  <option value="Yokohama Education Institute">Yokohama Education Institute</option>
                  <option value="Sapporo Technical College">Sapporo Technical College</option>
                </select>
              </div>
              
              <div>
                <label className="text-sm font-medium mb-2 block">Document Type</label>
                <select 
                  value={selectedDocumentType}
                  onChange={(e) => setSelectedDocumentType(e.target.value)}
                  className="w-full p-2 border rounded-md bg-background"
                >
                  <option value="all">All Document Types</option>
                  <option value="ID Card">ID Card</option>
                  <option value="Graduation Certificate">Graduation Certificate</option>
                  <option value="Transcript">Transcript</option>
                  <option value="Passport Copy">Passport Copy</option>
                  <option value="Medical Certificate">Medical Certificate</option>
                </select>
              </div>
              
              <div>
                <label className="text-sm font-medium mb-2 block">Status</label>
                <select 
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full p-2 border rounded-md bg-background"
                >
                  <option value="all">All Statuses</option>
                  <option value="verified">Verified</option>
                  <option value="pending">Pending</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Status Distribution Chart */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Status Distribution
              </CardTitle>
              <CardDescription>
                Visual representation of document verification status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-medium">Verified</span>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-green-600">
                      {Math.round((statusSummary.verified / statusSummary.total) * 100)}%
                    </div>
                    <div className="text-xs text-muted-foreground">{statusSummary.verified} documents</div>
                  </div>
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div 
                    className="bg-green-500 h-3 rounded-full transition-all duration-300" 
                    style={{ width: `${(statusSummary.verified / statusSummary.total) * 100}%` }}
                  ></div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
                    <span className="text-sm font-medium">Pending</span>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-yellow-600">
                      {Math.round((statusSummary.pending / statusSummary.total) * 100)}%
                    </div>
                    <div className="text-xs text-muted-foreground">{statusSummary.pending} documents</div>
                  </div>
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div 
                    className="bg-yellow-500 h-3 rounded-full transition-all duration-300" 
                    style={{ width: `${(statusSummary.pending / statusSummary.total) * 100}%` }}
                  ></div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                    <span className="text-sm font-medium">Rejected</span>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-red-600">
                      {Math.round((statusSummary.rejected / statusSummary.total) * 100)}%
                    </div>
                    <div className="text-xs text-muted-foreground">{statusSummary.rejected} documents</div>
                  </div>
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div 
                    className="bg-red-500 h-3 rounded-full transition-all duration-300" 
                    style={{ width: `${(statusSummary.rejected / statusSummary.total) * 100}%` }}
                  ></div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Processing Time Analysis */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Processing Time Analysis
              </CardTitle>
              <CardDescription>
                Average processing times by document type
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium">Academic Records</span>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold">5.2 days</div>
                    <div className="text-xs text-muted-foreground">Average</div>
                  </div>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium">Identity Documents</span>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold">3.8 days</div>
                    <div className="text-xs text-muted-foreground">Average</div>
                  </div>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-purple-600" />
                    <span className="text-sm font-medium">Financial Records</span>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold">6.1 days</div>
                    <div className="text-xs text-muted-foreground">Average</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Document Status Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Document Verification Status
            </CardTitle>
            <CardDescription>
              Comprehensive view of all document verification requests
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-3 font-medium">Institute</th>
                    <th className="text-left p-3 font-medium">Student</th>
                    <th className="text-left p-3 font-medium">Document Type</th>
                    <th className="text-left p-3 font-medium">Status</th>
                    <th className="text-left p-3 font-medium">Priority</th>
                    <th className="text-left p-3 font-medium">Last Updated</th>
                    <th className="text-left p-3 font-medium">Verified By</th>
                    <th className="text-left p-3 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredDocuments.map((document) => (
                    <tr key={document.id} className="border-b hover:bg-muted/50">
                      <td className="p-3">
                        <div className="font-medium">{document.institute}</div>
                      </td>
                      <td className="p-3">
                        <div>
                          <div className="font-medium">{document.studentName}</div>
                          <div className="text-xs text-muted-foreground">{document.studentId}</div>
                        </div>
                      </td>
                      <td className="p-3">
                        <div className="font-medium">{document.documentType}</div>
                      </td>
                      <td className="p-3">
                        <Badge className={getStatusColor(document.status)}>
                          {getStatusIcon(document.status)}
                          {document.status.charAt(0).toUpperCase() + document.status.slice(1)}
                        </Badge>
                      </td>
                      <td className="p-3">
                        <Badge variant="outline" className={getPriorityColor(document.priority)}>
                          {document.priority.charAt(0).toUpperCase() + document.priority.slice(1)}
                        </Badge>
                      </td>
                      <td className="p-3">
                        <div className="text-sm">
                          <div className="font-medium">{document.lastUpdated}</div>
                          <div className="text-xs text-muted-foreground">{document.processingTime}</div>
                        </div>
                      </td>
                      <td className="p-3">
                        <div className="text-sm font-medium">{document.verifiedBy}</div>
                      </td>
                      <td className="p-3">
                        <div className="flex items-center gap-2">
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleViewDetails(document)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Document Details Modal */}
        {showModal && selectedDocument && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-background rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Document Details</h2>
                  <Button variant="ghost" size="sm" onClick={closeModal}>
                    <XCircle className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Institute</label>
                      <p className="font-medium">{selectedDocument.institute}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Student Name</label>
                      <p className="font-medium">{selectedDocument.studentName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Document Type</label>
                      <p className="font-medium">{selectedDocument.documentType}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Status</label>
                      <Badge className={getStatusColor(selectedDocument.status)}>
                        {getStatusIcon(selectedDocument.status)}
                        {selectedDocument.status.charAt(0).toUpperCase() + selectedDocument.status.slice(1)}
                      </Badge>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Submitted Date</label>
                      <p className="font-medium">{selectedDocument.submittedDate}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                      <p className="font-medium">{selectedDocument.lastUpdated}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Verified By</label>
                      <p className="font-medium">{selectedDocument.verifiedBy}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Priority</label>
                      <Badge variant="outline" className={getPriorityColor(selectedDocument.priority)}>
                        {selectedDocument.priority.charAt(0).toUpperCase() + selectedDocument.priority.slice(1)}
                      </Badge>
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Verification Notes</label>
                    <p className="mt-1 p-3 bg-muted/50 rounded-lg">{selectedDocument.verificationNotes}</p>
                  </div>
                  
                  <div className="flex justify-end gap-2 pt-4 border-t">
                    <Button variant="outline" onClick={closeModal}>
                      Close
                    </Button>
                    <Button>
                      Take Action
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminDashboardLayout>
  );
}

import AdminDashboardLayout from "@/layouts/admin-dashboard-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  LayoutDashboard, 
  Building2, 
  Users, 
  GraduationCap, 
  FileText, 
  DollarSign,
  ArrowRight
} from "lucide-react";
import Link from "next/link";

export default function AdminDashboard() {
    const quickActions = [
        {
            title: "System Overview",
            description: "Monitor system performance and health",
            icon: LayoutDashboard,
            href: "/superadmin/dashboard/system-overview",
            color: "bg-blue-500"
        },
        {
            title: "Institute Performance",
            description: "Track institute metrics and analytics",
            icon: Building2,
            href: "/superadmin/dashboard/institute-performance",
            color: "bg-green-500"
        },
        {
            title: "Admin Activity",
            description: "Monitor admin actions and logs",
            icon: Users,
            href: "/superadmin/dashboard/admin-activity",
            color: "bg-purple-500"
        },
        {
            title: "Student Insights",
            description: "View student analytics and trends",
            icon: GraduationCap,
            href: "/superadmin/dashboard/student-insights",
            color: "bg-orange-500"
        },
        {
            title: "Document Status",
            description: "Track document processing status",
            icon: FileText,
            href: "/superadmin/dashboard/document-status",
            color: "bg-red-500"
        },
        {
            title: "Financial Overview",
            description: "Monitor revenue and financial metrics",
            icon: DollarSign,
            href: "/superadmin/dashboard/financial-overview",
            color: "bg-emerald-500"
        }
    ];

    return (
        <AdminDashboardLayout>
            <div className="flex flex-1 flex-col gap-6 p-6">
                {/* Welcome Header */}
                <div className="text-center">
                    <h1 className="text-4xl font-bold tracking-tight mb-2">
                        Welcome to Super Admin Dashboard
                    </h1>
                    <p className="text-xl text-muted-foreground">
                        Manage your entire system from one central location
                    </p>
                </div>

                {/* Quick Actions Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {quickActions.map((action) => (
                        <Card key={action.title} className="hover:shadow-lg transition-all duration-200">
                            <CardHeader>
                                <div className="flex items-center gap-3">
                                    <div className={`p-2 rounded-lg ${action.color}`}>
                                        <action.icon className="h-6 w-6 text-white" />
                                    </div>
                                    <CardTitle className="text-lg">{action.title}</CardTitle>
                                </div>
                                <CardDescription>{action.description}</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <Button asChild className="w-full">
                                    <Link href={action.href}>
                                        Access {action.title}
                                        <ArrowRight className="ml-2 h-4 w-4" />
                                    </Link>
                                </Button>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* Quick Stats */}
                <div className="grid gap-4 md:grid-cols-4">
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">1,247</div>
                            <p className="text-xs text-muted-foreground">+12% from last month</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Active Institutes</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">23</div>
                            <p className="text-xs text-muted-foreground">+2 new this month</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">System Uptime</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">99.8%</div>
                            <p className="text-xs text-muted-foreground">Last 30 days</p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Active Sessions</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">89</div>
                            <p className="text-xs text-muted-foreground">Currently online</p>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AdminDashboardLayout>
    );
}

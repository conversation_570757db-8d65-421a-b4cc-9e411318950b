import React, { useState } from 'react';
import AdminDashboardLayout from "@/layouts/admin-dashboard-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  GraduationCap, 
  Users, 
  TrendingUp, 
  TrendingDown, 
  BookOpen, 
  Award,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Eye,
  Filter,
  Download,
  BarChart3,
  <PERSON><PERSON>hart,
  LineChart,
  Target,
  Star,
  Globe,
  Calendar,
  MapPin,
  Building2,
  Activity
} from "lucide-react";

// Mock data - in a real app, this would come from API calls
const studentData = {
  totalStudents: 4870,
  activeStudents: 4230,
  graduatedStudents: 640,
  newEnrollments: 320,
  averageGPA: 3.42,
  completionRate: 87.5,
  satisfactionScore: 4.3,
  employmentRate: 89.2
};

const enrollmentTrends = [
  { month: "Jan", count: 120, growth: "+8%" },
  { month: "Feb", count: 135, growth: "+12%" },
  { month: "Mar", count: 142, growth: "+5%" },
  { month: "Apr", count: 158, growth: "+11%" },
  { month: "May", count: 165, growth: "+4%" },
  { month: "Jun", count: 178, growth: "+8%" },
  { month: "Jul", count: 185, growth: "+4%" },
  { month: "Aug", count: 320, growth: "+73%" },
  { month: "Sep", count: 298, growth: "-7%" },
  { month: "Oct", count: 285, growth: "-4%" },
  { month: "Nov", count: 275, growth: "-4%" },
  { month: "Dec", count: 268, growth: "-3%" }
];

const institutePerformance = [
  {
    name: "Nihon Institute of Technology",
    totalStudents: 1250,
    averageGPA: 3.58,
    completionRate: 92.3,
    satisfactionScore: 4.6,
    employmentRate: 94.1,
    growth: "+15%",
    status: "excellent"
  },
  {
    name: "Osaka Business Academy",
    totalStudents: 890,
    averageGPA: 3.42,
    completionRate: 88.7,
    satisfactionScore: 4.3,
    employmentRate: 91.2,
    growth: "+8%",
    status: "good"
  },
  {
    name: "Kyoto Learning Center",
    totalStudents: 1100,
    averageGPA: 3.51,
    completionRate: 90.1,
    satisfactionScore: 4.7,
    employmentRate: 93.5,
    growth: "+12%",
    status: "excellent"
  },
  {
    name: "Yokohama Education Institute",
    totalStudents: 650,
    averageGPA: 3.18,
    completionRate: 78.9,
    satisfactionScore: 3.9,
    employmentRate: 82.3,
    growth: "-3%",
    status: "needs_improvement"
  },
  {
    name: "Sapporo Technical College",
    totalStudents: 980,
    averageGPA: 3.39,
    completionRate: 86.4,
    satisfactionScore: 4.4,
    employmentRate: 89.8,
    growth: "+9%",
    status: "good"
  }
];

const coursePerformance = [
  { name: "Computer Science", students: 890, avgGPA: 3.52, completionRate: 91.2, employmentRate: 96.8 },
  { name: "Business Administration", students: 750, avgGPA: 3.38, completionRate: 88.9, employmentRate: 92.3 },
  { name: "Engineering", students: 680, avgGPA: 3.45, completionRate: 89.7, employmentRate: 94.1 },
  { name: "Arts & Humanities", students: 420, avgGPA: 3.28, completionRate: 85.3, employmentRate: 87.6 },
  { name: "Medical Sciences", students: 380, avgGPA: 3.61, completionRate: 93.1, employmentRate: 97.2 },
  { name: "Language Studies", students: 320, avgGPA: 3.35, completionRate: 87.8, employmentRate: 89.4 }
];

const demographicData = {
  ageGroups: [
    { range: "18-22", count: 2150, percentage: 44.1 },
    { range: "23-27", count: 1680, percentage: 34.5 },
    { range: "28-32", count: 720, percentage: 14.8 },
    { range: "33+", count: 320, percentage: 6.6 }
  ],
  genderDistribution: [
    { gender: "Male", count: 2435, percentage: 50.0 },
    { gender: "Female", count: 2435, percentage: 50.0 }
  ],
  internationalStudents: 1240,
  internationalPercentage: 25.5
};

const topPerformers = [
  { name: "Akira Yamamoto", institute: "Nihon Institute", course: "Computer Science", gpa: 3.98, achievements: 5 },
  { name: "Mei Chen", institute: "Osaka Business Academy", course: "Business Admin", gpa: 3.95, achievements: 4 },
  { name: "Kenji Suzuki", institute: "Kyoto Learning Center", course: "Engineering", gpa: 3.92, achievements: 6 },
  { name: "Hanako Sato", institute: "Sapporo Technical College", course: "Medical Sciences", gpa: 3.89, achievements: 3 },
  { name: "Takeshi Nakamura", institute: "Yokohama Education Institute", course: "Arts & Humanities", gpa: 3.87, achievements: 4 }
];

export default function StudentInsights() {
  const [selectedInstitute, setSelectedInstitute] = useState<string>("all");
  const [selectedCourse, setSelectedCourse] = useState<string>("all");

  const getStatusColor = (status: string) => {
    switch (status) {
      case "excellent": return "text-green-600 bg-green-50 dark:bg-green-950";
      case "good": return "text-blue-600 bg-blue-50 dark:bg-blue-950";
      case "needs_improvement": return "text-yellow-600 bg-yellow-50 dark:bg-yellow-950";
      case "poor": return "text-red-600 bg-red-50 dark:bg-red-950";
      default: return "text-gray-600 bg-gray-50 dark:bg-gray-950";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "excellent": return <Award className="h-4 w-4" />;
      case "good": return <CheckCircle className="h-4 w-4" />;
      case "needs_improvement": return <AlertTriangle className="h-4 w-4" />;
      case "poor": return <XCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getGrowthIcon = (growth: string) => {
    return growth.startsWith('+') ? 
      <TrendingUp className="h-4 w-4 text-green-600" /> : 
      <TrendingDown className="h-4 w-4 text-red-600" />;
  };

  const getPerformanceColor = (score: number) => {
    if (score >= 3.5) return "text-green-600";
    if (score >= 3.0) return "text-blue-600";
    if (score >= 2.5) return "text-yellow-600";
    return "text-red-600";
  };

  const filteredInstitutePerformance = selectedInstitute === "all" 
    ? institutePerformance 
    : institutePerformance.filter(inst => inst.name === selectedInstitute);

  const filteredCoursePerformance = selectedCourse === "all" 
    ? coursePerformance 
    : coursePerformance.filter(course => course.name === selectedCourse);

  return (
    <AdminDashboardLayout>
      <div className="flex flex-1 flex-col gap-6 p-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Student Insights</h1>
            <p className="text-muted-foreground">
              Comprehensive analytics and insights about student performance across all institutes
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>

        {/* Key Metrics Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Students</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{studentData.totalStudents.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+{studentData.newEnrollments}</span> new this semester
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average GPA</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{studentData.averageGPA}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+0.12</span> from last semester
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{studentData.completionRate}%</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+2.1%</span> improvement
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Employment Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{studentData.employmentRate}%</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+1.8%</span> from last year
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Enrollment Trends and Demographics */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Enrollment Trends */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className="h-5 w-5" />
                Enrollment Trends
              </CardTitle>
              <CardDescription>
                Monthly student enrollment patterns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {enrollmentTrends.slice(-6).map((trend) => (
                  <div key={trend.month} className="flex items-center justify-between">
                    <span className="text-sm font-medium">{trend.month}</span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">{trend.count}</span>
                      <span className={`text-xs ${trend.growth.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                        {trend.growth}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Demographics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5" />
                Student Demographics
              </CardTitle>
              <CardDescription>
                Age distribution and international students
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>International Students</span>
                  <span className="font-medium">{demographicData.internationalStudents} ({demographicData.internationalPercentage}%)</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${demographicData.internationalPercentage}%` }}
                  ></div>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-sm font-medium mb-2">Age Distribution</div>
                {demographicData.ageGroups.map((group) => (
                  <div key={group.range} className="flex justify-between text-xs">
                    <span>{group.range} years</span>
                    <span>{group.count} ({group.percentage}%)</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Institute Performance Comparison */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Institute Performance Comparison
            </CardTitle>
            <CardDescription>
              Student performance metrics across all institutes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-3 font-medium">Institute</th>
                    <th className="text-left p-3 font-medium">Students</th>
                    <th className="text-left p-3 font-medium">Avg GPA</th>
                    <th className="text-left p-3 font-medium">Completion Rate</th>
                    <th className="text-left p-3 font-medium">Satisfaction</th>
                    <th className="text-left p-3 font-medium">Employment</th>
                    <th className="text-left p-3 font-medium">Growth</th>
                    <th className="text-left p-3 font-medium">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredInstitutePerformance.map((institute) => (
                    <tr key={institute.name} className="border-b hover:bg-muted/50">
                      <td className="p-3">
                        <div className="font-medium">{institute.name}</div>
                      </td>
                      <td className="p-3">
                        <div className="text-sm font-medium">{institute.totalStudents.toLocaleString()}</div>
                      </td>
                      <td className="p-3">
                        <div className={`text-sm font-medium ${getPerformanceColor(institute.averageGPA)}`}>
                          {institute.averageGPA}
                        </div>
                      </td>
                      <td className="p-3">
                        <div className="text-sm font-medium">{institute.completionRate}%</div>
                      </td>
                      <td className="p-3">
                        <div className="flex items-center gap-1">
                          <span className="text-sm font-medium">{institute.satisfactionScore}</span>
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        </div>
                      </td>
                      <td className="p-3">
                        <div className="text-sm font-medium">{institute.employmentRate}%</div>
                      </td>
                      <td className="p-3">
                        <div className="flex items-center gap-1">
                          {getGrowthIcon(institute.growth)}
                          <span className={`text-sm font-medium ${institute.growth.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                            {institute.growth}
                          </span>
                        </div>
                      </td>
                      <td className="p-3">
                        <Badge className={getStatusColor(institute.status)}>
                          {getStatusIcon(institute.status)}
                          {institute.status.replace('_', ' ')}
                        </Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Course Performance and Top Performers */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Course Performance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Course Performance
              </CardTitle>
              <CardDescription>
                Student performance by course of study
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredCoursePerformance.map((course) => (
                  <div key={course.name} className="p-3 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">{course.name}</h3>
                      <span className="text-sm text-muted-foreground">{course.students} students</span>
                    </div>
                    <div className="grid grid-cols-3 gap-2 text-xs">
                      <div>
                        <div className="text-muted-foreground">Avg GPA</div>
                        <div className={`font-medium ${getPerformanceColor(course.avgGPA)}`}>
                          {course.avgGPA}
                        </div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Completion</div>
                        <div className="font-medium">{course.completionRate}%</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Employment</div>
                        <div className="font-medium">{course.employmentRate}%</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Top Performers */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" />
                Top Performing Students
              </CardTitle>
              <CardDescription>
                Students with highest academic achievements
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {topPerformers.map((student, index) => (
                  <div key={student.name} className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <span className="text-sm font-bold text-primary">{index + 1}</span>
                    </div>
                    <div className="flex-1">
                      <div className="font-medium">{student.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {student.institute} • {student.course}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-600">{student.gpa}</div>
                      <div className="text-xs text-muted-foreground">GPA</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Student Satisfaction and Engagement */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5" />
              Student Satisfaction & Engagement
            </CardTitle>
            <CardDescription>
              Overall student experience and engagement metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-3">
              <div className="text-center p-4 bg-green-50 dark:bg-green-950 rounded-lg">
                <Star className="h-8 w-8 mx-auto mb-2 text-green-600" />
                <h3 className="font-semibold text-green-800 dark:text-green-200">Overall Satisfaction</h3>
                <p className="text-2xl font-bold text-green-600">{studentData.satisfactionScore}/5.0</p>
                <p className="text-sm text-green-600 dark:text-green-400">Based on 4,200+ reviews</p>
              </div>
              
              <div className="text-center p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
                <Activity className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                <h3 className="font-semibold text-blue-800 dark:text-blue-200">Active Engagement</h3>
                <p className="text-2xl font-bold text-blue-600">87.3%</p>
                <p className="text-sm text-blue-600 dark:text-blue-400">Regular participation rate</p>
              </div>
              
              <div className="text-center p-4 bg-purple-50 dark:bg-purple-950 rounded-lg">
                <Target className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                <h3 className="font-semibold text-purple-800 dark:text-purple-200">Goal Achievement</h3>
                <p className="text-2xl font-bold text-purple-600">92.1%</p>
                <p className="text-sm text-purple-600 dark:text-purple-400">Students meeting targets</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminDashboardLayout>
  );
}

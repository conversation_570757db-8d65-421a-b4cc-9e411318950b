import React, { useState } from 'react';
import AdminDashboardLayout from "@/layouts/admin-dashboard-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  DollarSign, TrendingUp, CreditCard, Banknote, 
  Eye, Filter, Download, Clock, CheckCircle, XCircle,
  ArrowUpRight, ArrowDownRight, BarChart3, MoreHorizontal
} from "lucide-react";

// Mock data
const financialData = [
  {
    id: 1,
    institute: "Nihon Institute of Technology",
    transactionType: "subscription",
    amount: 15000,
    currency: "INR",
    date: "2024-08-16",
    status: "processed",
    paymentMethod: "Credit Card",
    studentName: "<PERSON>",
    studentId: "STU-2024-001",
    description: "Monthly subscription payment for advanced courses",
    reference: "SUB-2024-001",
    processingTime: "2 hours",
    adminName: "<PERSON><PERSON>"
  },
  {
    id: 2,
    institute: "Osaka Business Academy",
    transactionType: "payout",
    amount: 50000,
    currency: "INR",
    date: "2024-08-15",
    status: "pending",
    paymentMethod: "Bank Transfer",
    studentName: "Mei Chen",
    studentId: "STU-2024-002",
    description: "Scholarship payout for academic excellence",
    reference: "PAY-2024-002",
    processingTime: "24 hours",
    adminName: "Hiroshi Sato"
  },
  {
    id: 3,
    institute: "Kyoto Learning Center",
    transactionType: "subscription",
    amount: 20000,
    currency: "INR",
    date: "2024-08-14",
    status: "processed",
    paymentMethod: "PayPal",
    studentName: "Kenji Suzuki",
    studentId: "STU-2024-003",
    description: "Premium subscription for specialized training",
    reference: "SUB-2024-003",
    processingTime: "1 hour",
    adminName: "Akiko Watanabe"
  },
  {
    id: 4,
    institute: "Yokohama Education Institute",
    transactionType: "payout",
    amount: 30000,
    currency: "INR",
    date: "2024-08-13",
    status: "rejected",
    paymentMethod: "Bank Transfer",
    studentName: "Hanako Sato",
    studentId: "STU-2024-004",
    description: "Refund for cancelled course enrollment",
    reference: "PAY-2024-004",
    processingTime: "48 hours",
    adminName: "Masato Kimura",
    rejectionReason: "Insufficient account balance for payout"
  },
  {
    id: 5,
    institute: "Sapporo Technical College",
    transactionType: "subscription",
    amount: 17500,
    currency: "INR",
    date: "2024-08-12",
    status: "processed",
    paymentMethod: "Credit Card",
    studentName: "Takeshi Nakamura",
    studentId: "STU-2024-005",
    description: "Annual subscription for technical certification",
    reference: "SUB-2024-005",
    processingTime: "3 hours",
    adminName: "Yumi Nakamura"
  }
];

// Financial summary calculations
const financialSummary = {
  totalRevenue: financialData.filter(t => t.transactionType === "subscription" && t.status === "processed").reduce((sum, t) => sum + t.amount, 0),
  totalPayouts: financialData.filter(t => t.transactionType === "payout" && t.status === "processed").reduce((sum, t) => sum + t.amount, 0),
  pendingPayouts: financialData.filter(t => t.transactionType === "payout" && t.status === "pending").reduce((sum, t) => sum + t.amount, 0),
  rejectedPayouts: financialData.filter(t => t.transactionType === "payout" && t.status === "rejected").reduce((sum, t) => sum + t.amount, 0),
  netRevenue: 0,
  totalTransactions: financialData.length,
  processedTransactions: financialData.filter(t => t.status === "processed").length,
  pendingTransactions: financialData.filter(t => t.status === "pending").length,
  rejectedTransactions: financialData.filter(t => t.status === "rejected").length
};

financialSummary.netRevenue = financialSummary.totalRevenue - financialSummary.totalPayouts;

// Monthly earnings data
const monthlyEarnings = [
  { month: "March", earnings: 50000, growth: 0 },
  { month: "April", earnings: 60000, growth: 20 },
  { month: "May", earnings: 70000, growth: 16.7 },
  { month: "June", earnings: 80000, growth: 14.3 },
  { month: "July", earnings: 90000, growth: 12.5 },
  { month: "August", earnings: 100000, growth: 11.1 }
];

export default function FinancialOverview() {
  const [selectedInstitute, setSelectedInstitute] = useState<string>("all");
  const [selectedTransactionType, setSelectedTransactionType] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [selectedDateRange, setSelectedDateRange] = useState<string>("all");
  const [selectedTransaction, setSelectedTransaction] = useState<any>(null);
  const [showModal, setShowModal] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "processed": return "text-green-600 bg-green-50 dark:bg-green-950";
      case "pending": return "text-yellow-600 bg-yellow-50 dark:bg-yellow-950";
      case "rejected": return "text-red-600 bg-red-50 dark:bg-red-950";
      default: return "text-gray-600 bg-gray-50 dark:bg-gray-950";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "processed": return <CheckCircle className="h-4 w-4" />;
      case "pending": return <Clock className="h-4 w-4" />;
      case "rejected": return <XCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getTransactionTypeIcon = (type: string) => {
    switch (type) {
      case "subscription": return <CreditCard className="h-4 w-4" />;
      case "payout": return <Banknote className="h-4 w-4" />;
      default: return <DollarSign className="h-4 w-4" />;
    }
  };

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case "subscription": return "text-blue-600 bg-blue-50 dark:bg-blue-950";
      case "payout": return "text-purple-600 bg-purple-50 dark:bg-purple-950";
      default: return "text-gray-600 bg-gray-50 dark:bg-gray-950";
    }
  };

  const filteredTransactions = financialData.filter(transaction => {
    const instituteMatch = selectedInstitute === "all" || transaction.institute === selectedInstitute;
    const typeMatch = selectedTransactionType === "all" || transaction.transactionType === selectedTransactionType;
    const statusMatch = selectedStatus === "all" || transaction.status === selectedStatus;
    
    let dateMatch = true;
    if (selectedDateRange !== "all") {
      const transactionDate = new Date(transaction.date);
      const today = new Date();
      const daysDiff = Math.floor((today.getTime() - transactionDate.getTime()) / (1000 * 60 * 60 * 24));
      
      switch (selectedDateRange) {
        case "today": dateMatch = daysDiff === 0; break;
        case "week": dateMatch = daysDiff <= 7; break;
        case "month": dateMatch = daysDiff <= 30; break;
        case "quarter": dateMatch = daysDiff <= 90; break;
      }
    }
    
    return instituteMatch && typeMatch && statusMatch && dateMatch;
  });

  const handleViewDetails = (transaction: any) => {
    setSelectedTransaction(transaction);
    setShowModal(true);
  };

  const handleExportCSV = () => {
    console.log("Exporting financial data to CSV...");
    alert("Financial data exported to CSV successfully!");
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedTransaction(null);
  };

  const formatCurrency = (amount: number, currency: string = "INR") => {
    return `Rs ${amount.toLocaleString('en-IN')}`;
  };

  return (
    <AdminDashboardLayout>
      <div className="flex flex-1 flex-col gap-6 p-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Financial Overview</h1>
            <p className="text-muted-foreground">
              Monitor financial metrics and transactions across all institutes
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Advanced Filter
            </Button>
            <Button onClick={handleExportCSV} size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </div>

        {/* Financial Summary Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(financialSummary.totalRevenue)}
              </div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600 flex items-center gap-1">
                  <ArrowUpRight className="h-3 w-3" />
                  +12.5%
                </span>
                from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Payouts</CardTitle>
              <Banknote className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {formatCurrency(financialSummary.totalPayouts)}
              </div>
              <p className="text-xs text-muted-foreground">
                <span className="text-purple-600 flex items-center gap-1">
                  <ArrowUpRight className="h-3 w-3" />
                  +8.2%
                </span>
                from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Net Revenue</CardTitle>
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {formatCurrency(financialSummary.netRevenue)}
              </div>
              <p className="text-xs text-muted-foreground">
                <span className="text-blue-600 flex items-center gap-1">
                  <ArrowUpRight className="h-3 w-3" />
                  +15.3%
                </span>
                from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Payouts</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {formatCurrency(financialSummary.pendingPayouts)}
              </div>
              <p className="text-xs text-muted-foreground">
                <span className="text-yellow-600 flex items-center gap-1">
                  <ArrowDownRight className="h-3 w-3" />
                  -5.1%
                </span>
                from last month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Transaction Statistics */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium">Transaction Volume</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{financialSummary.totalTransactions}</div>
              <p className="text-xs text-muted-foreground">Total transactions this month</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {Math.round((financialSummary.processedTransactions / financialSummary.totalTransactions) * 100)}%
              </div>
              <p className="text-xs text-muted-foreground">
                {financialSummary.processedTransactions} of {financialSummary.totalTransactions} processed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium">Rejection Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {Math.round((financialSummary.rejectedTransactions / financialSummary.totalTransactions) * 100)}%
              </div>
              <p className="text-xs text-muted-foreground">
                {financialSummary.rejectedTransactions} of {financialSummary.totalTransactions} rejected
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filter Transactions
            </CardTitle>
            <CardDescription>
              Filter financial transactions by various criteria
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Institute</label>
                <select 
                  value={selectedInstitute}
                  onChange={(e) => setSelectedInstitute(e.target.value)}
                  className="w-full p-2 border rounded-md bg-background"
                >
                  <option value="all">All Institutes</option>
                  <option value="Nihon Institute of Technology">Nihon Institute of Technology</option>
                  <option value="Osaka Business Academy">Osaka Business Academy</option>
                  <option value="Kyoto Learning Center">Kyoto Learning Center</option>
                  <option value="Yokohama Education Institute">Yokohama Education Institute</option>
                  <option value="Sapporo Technical College">Sapporo Technical College</option>
                </select>
              </div>
              
              <div>
                <label className="text-sm font-medium mb-2 block">Transaction Type</label>
                <select 
                  value={selectedTransactionType}
                  onChange={(e) => setSelectedTransactionType(e.target.value)}
                  className="w-full p-2 border rounded-md bg-background"
                >
                  <option value="all">All Types</option>
                  <option value="subscription">Subscription</option>
                  <option value="payout">Payout</option>
                </select>
              </div>
              
              <div>
                <label className="text-sm font-medium mb-2 block">Status</label>
                <select 
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full p-2 border rounded-md bg-background"
                >
                  <option value="all">All Statuses</option>
                  <option value="processed">Processed</option>
                  <option value="pending">Pending</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Date Range</label>
                <select 
                  value={selectedDateRange}
                  onChange={(e) => setSelectedDateRange(e.target.value)}
                  className="w-full p-2 border rounded-md bg-background"
                >
                  <option value="all">All Time</option>
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                  <option value="quarter">This Quarter</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Charts Section */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Earnings Trend Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Earnings Trend
              </CardTitle>
              <CardDescription>
                Monthly earnings over the last 6 months
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {monthlyEarnings.map((month, index) => (
                  <div key={month.month} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="text-sm font-medium">{month.month}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-blue-600">
                        {formatCurrency(month.earnings)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {month.growth > 0 ? (
                          <span className="text-green-600 flex items-center gap-1">
                            <ArrowUpRight className="h-3 w-3" />
                            +{month.growth}%
                          </span>
                        ) : (
                          <span className="text-gray-600">No change</span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                
                {/* Simple bar chart representation */}
                <div className="mt-6 space-y-2">
                  <div className="text-sm font-medium text-center mb-3">Visual Representation</div>
                  <div className="flex items-end justify-between h-32 gap-2">
                    {monthlyEarnings.map((month, index) => (
                      <div key={month.month} className="flex-1 flex flex-col items-center">
                        <div 
                          className="w-full bg-blue-500 rounded-t transition-all duration-300 hover:bg-blue-600"
                          style={{ height: `${(month.earnings / 100000) * 100}%` }}
                        ></div>
                        <div className="text-xs text-muted-foreground mt-2 text-center">
                          {month.month.slice(0, 3)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Transaction Distribution */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Transaction Distribution
              </CardTitle>
              <CardDescription>
                Breakdown by transaction type and status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium">Subscriptions</span>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-blue-600">
                      {financialData.filter(t => t.transactionType === "subscription").length}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {formatCurrency(financialData.filter(t => t.transactionType === "subscription").reduce((sum, t) => sum + t.amount, 0))}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Banknote className="h-4 w-4 text-purple-600" />
                    <span className="text-sm font-medium">Payouts</span>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-purple-600">
                      {financialData.filter(t => t.transactionType === "payout").length}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {formatCurrency(financialData.filter(t => t.transactionType === "payout").reduce((sum, t) => sum + t.amount, 0))}
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium">Processed</span>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-green-600">
                      {financialSummary.processedTransactions}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {Math.round((financialSummary.processedTransactions / financialSummary.totalTransactions) * 100)}% success rate
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Financial Transactions Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Financial Transactions
            </CardTitle>
            <CardDescription>
              Comprehensive view of all financial transactions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-3 font-medium">Institute</th>
                    <th className="text-left p-3 font-medium">Transaction Type</th>
                    <th className="text-left p-3 font-medium">Amount</th>
                    <th className="text-left p-3 font-medium">Date</th>
                    <th className="text-left p-3 font-medium">Status</th>
                    <th className="text-left p-3 font-medium">Payment Method</th>
                    <th className="text-left p-3 font-medium">Student</th>
                    <th className="text-left p-3 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredTransactions.map((transaction) => (
                    <tr key={transaction.id} className="border-b hover:bg-muted/50">
                      <td className="p-3">
                        <div className="font-medium">{transaction.institute}</div>
                      </td>
                      <td className="p-3">
                        <Badge className={getTransactionTypeColor(transaction.transactionType)}>
                          {getTransactionTypeIcon(transaction.transactionType)}
                          {transaction.transactionType.charAt(0).toUpperCase() + transaction.transactionType.slice(1)}
                        </Badge>
                      </td>
                      <td className="p-3">
                        <div className="font-medium">
                          {formatCurrency(transaction.amount, transaction.currency)}
                        </div>
                      </td>
                      <td className="p-3">
                        <div className="text-sm">
                          <div className="font-medium">{transaction.date}</div>
                          <div className="text-xs text-muted-foreground">{transaction.processingTime}</div>
                        </div>
                      </td>
                      <td className="p-3">
                        <Badge className={getStatusColor(transaction.status)}>
                          {getStatusIcon(transaction.status)}
                          {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                        </Badge>
                      </td>
                      <td className="p-3">
                        <div className="text-sm font-medium">{transaction.paymentMethod}</div>
                      </td>
                      <td className="p-3">
                        <div>
                          <div className="font-medium">{transaction.studentName}</div>
                          <div className="text-xs text-muted-foreground">{transaction.studentId}</div>
                        </div>
                      </td>
                      <td className="p-3">
                        <div className="flex items-center gap-2">
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleViewDetails(transaction)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Transaction Details Modal */}
        {showModal && selectedTransaction && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-background rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold">Transaction Details</h2>
                  <Button variant="ghost" size="sm" onClick={closeModal}>
                    <XCircle className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Institute</label>
                      <p className="font-medium">{selectedTransaction.institute}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Transaction Type</label>
                      <Badge className={getTransactionTypeColor(selectedTransaction.transactionType)}>
                        {getTransactionTypeIcon(selectedTransaction.transactionType)}
                        {selectedTransaction.transactionType.charAt(0).toUpperCase() + selectedTransaction.transactionType.slice(1)}
                      </Badge>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Amount</label>
                      <p className="font-medium text-lg">
                        {formatCurrency(selectedTransaction.amount, selectedTransaction.currency)}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Status</label>
                      <Badge className={getStatusColor(selectedTransaction.status)}>
                        {getStatusIcon(selectedTransaction.status)}
                        {selectedTransaction.status.charAt(0).toUpperCase() + selectedTransaction.status.slice(1)}
                      </Badge>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Date</label>
                      <p className="font-medium">{selectedTransaction.date}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Payment Method</label>
                      <p className="font-medium">{selectedTransaction.paymentMethod}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Student Name</label>
                      <p className="font-medium">{selectedTransaction.studentName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Student ID</label>
                      <p className="font-medium">{selectedTransaction.studentId}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Reference</label>
                      <p className="font-medium">{selectedTransaction.reference}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Processing Time</label>
                      <p className="font-medium">{selectedTransaction.processingTime}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Admin Name</label>
                      <p className="font-medium">{selectedTransaction.adminName}</p>
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Description</label>
                    <p className="mt-1 p-3 bg-muted/50 rounded-lg">{selectedTransaction.description}</p>
                  </div>

                  {selectedTransaction.rejectionReason && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Rejection Reason</label>
                      <p className="mt-1 p-3 bg-red-50 dark:bg-red-950/20 rounded-lg text-red-700 dark:text-red-300">
                        {selectedTransaction.rejectionReason}
                      </p>
                    </div>
                  )}
                  
                  <div className="flex justify-end gap-2 pt-4 border-t">
                    <Button variant="outline" onClick={closeModal}>
                      Close
                    </Button>
                    <Button>
                      Take Action
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminDashboardLayout>
  );
}

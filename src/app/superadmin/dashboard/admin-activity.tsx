import React, { useState } from 'react';
import AdminDashboardLayout from "@/layouts/admin-dashboard-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Users, 
  FileText, 
  DollarSign, 
  GraduationCap, 
  Building2, 
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  Filter,
  Download,
  Search,
  Calendar,
  TrendingUp,
  Activity,
  Shield,
  BookOpen,
  MessageSquare,
  Video,
  Settings,
  MoreHorizontal
} from "lucide-react";

// Mock data - in a real app, this would come from API calls
const adminActivities = [
  {
    id: 1,
    adminName: "Yu<PERSON>",
    institute: "Nihon Institute of Technology",
    action: "student_registration",
    actionLabel: "Student Registration",
    description: "Registered new student: <PERSON> (ID: STU-2024-001)",
    status: "completed",
    priority: "medium",
    timestamp: "2 minutes ago",
    duration: "5 min",
    category: "Student Management",
    details: {
      studentName: "<PERSON>",
      studentId: "STU-2024-001",
      course: "Computer Science",
      semester: "Fall 2024"
    }
  },
  {
    id: 2,
    adminName: "Hiroshi Sato",
    institute: "Osaka Business Academy",
    action: "document_verification",
    actionLabel: "Document Verification",
    description: "Verified graduation certificate for student: Mei Chen",
    status: "completed",
    priority: "high",
    timestamp: "15 minutes ago",
    duration: "12 min",
    category: "Document Verification",
    details: {
      studentName: "Mei Chen",
      documentType: "Graduation Certificate",
      verificationResult: "Approved",
      notes: "All documents verified successfully"
    }
  },
  {
    id: 3,
    adminName: "Akiko Watanabe",
    institute: "Kyoto Learning Center",
    action: "payment_processing",
    actionLabel: "Payment Processing",
    description: "Processed tuition payment for student: Kenji Suzuki",
    status: "completed",
    priority: "high",
    timestamp: "1 hour ago",
    duration: "8 min",
    category: "Payment Processing",
    details: {
      studentName: "Kenji Suzuki",
      amount: "¥450,000",
      paymentMethod: "Bank Transfer",
      transactionId: "TXN-2024-789"
    }
  },
  {
    id: 4,
    adminName: "Masato Kimura",
    institute: "Yokohama Education Institute",
    action: "course_management",
    actionLabel: "Course Management",
    description: "Updated course curriculum for Advanced Mathematics",
    status: "in_progress",
    priority: "medium",
    timestamp: "2 hours ago",
    duration: "25 min",
    category: "Course Management",
    details: {
      courseName: "Advanced Mathematics",
      changes: "Updated syllabus and added new modules",
      instructor: "Dr. Takeshi Nakamura"
    }
  },
  {
    id: 5,
    adminName: "Yumi Nakamura",
    institute: "Sapporo Technical College",
    action: "staff_management",
    actionLabel: "Staff Management",
    description: "Approved leave request for faculty member: Dr. Kenji Tanaka",
    status: "completed",
    priority: "low",
    timestamp: "3 hours ago",
    duration: "3 min",
    category: "Staff Management",
    details: {
      staffName: "Dr. Kenji Tanaka",
      leaveType: "Annual Leave",
      duration: "5 days",
      reason: "Family vacation"
    }
  },
  {
    id: 6,
    adminName: "Takashi Ito",
    institute: "Nihon Institute of Technology",
    action: "content_update",
    actionLabel: "Content Update",
    description: "Updated learning materials for Programming Fundamentals",
    status: "completed",
    priority: "medium",
    timestamp: "4 hours ago",
    duration: "15 min",
    category: "Content Management",
    details: {
      courseName: "Programming Fundamentals",
      materials: "Updated lecture slides and practice exercises",
      version: "v2.1"
    }
  },
  {
    id: 7,
    adminName: "Keiko Yamamoto",
    institute: "Osaka Business Academy",
    action: "post_management",
    actionLabel: "Post Management",
    description: "Published announcement: New Scholarship Program Available",
    status: "completed",
    priority: "high",
    timestamp: "5 hours ago",
    duration: "10 min",
    category: "Communication",
    details: {
      postTitle: "New Scholarship Program Available",
      postType: "Announcement",
      audience: "All Students",
      visibility: "Public"
    }
  },
  {
    id: 8,
    adminName: "Ryo Tanaka",
    institute: "Kyoto Learning Center",
    action: "class_scheduling",
    actionLabel: "Class Scheduling",
    description: "Scheduled online class: Japanese Language Advanced",
    status: "completed",
    priority: "medium",
    timestamp: "6 hours ago",
    duration: "20 min",
    category: "Class Management",
    details: {
      courseName: "Japanese Language Advanced",
      schedule: "Every Tuesday & Thursday, 2:00 PM - 4:00 PM",
      instructor: "Prof. Hanako Sato",
      platform: "Zoom"
    }
  },
  {
    id: 9,
    adminName: "Mika Suzuki",
    institute: "Yokohama Education Institute",
    action: "security_monitoring",
    actionLabel: "Security Monitoring",
    description: "Investigated suspicious login attempt from unknown IP",
    status: "completed",
    priority: "high",
    timestamp: "8 hours ago",
    duration: "30 min",
    category: "Security",
    details: {
      incidentType: "Suspicious Login Attempt",
      ipAddress: "*************",
      action: "Account temporarily locked",
      resolution: "Security alert sent to user"
    }
  },
  {
    id: 10,
    adminName: "Kazuki Abe",
    institute: "Sapporo Technical College",
    action: "data_export",
    actionLabel: "Data Export",
    description: "Exported student performance report for Q1 2024",
    status: "completed",
    priority: "low",
    timestamp: "12 hours ago",
    duration: "45 min",
    category: "Reporting",
    details: {
      reportType: "Student Performance Report",
      period: "Q1 2024",
      format: "Excel",
      recipients: "Board of Directors"
    }
  }
];

const activityCategories = [
  { name: "Student Management", count: 1, icon: Users, color: "bg-blue-500" },
  { name: "Document Verification", count: 1, icon: FileText, color: "bg-green-500" },
  { name: "Payment Processing", count: 1, icon: DollarSign, color: "bg-emerald-500" },
  { name: "Course Management", count: 1, icon: BookOpen, color: "bg-purple-500" },
  { name: "Staff Management", count: 1, icon: Users, color: "bg-orange-500" },
  { name: "Content Management", count: 1, icon: BookOpen, color: "bg-indigo-500" },
  { name: "Communication", count: 1, icon: MessageSquare, color: "bg-pink-500" },
  { name: "Class Management", count: 1, icon: Video, color: "bg-cyan-500" },
  { name: "Security", count: 1, icon: Shield, color: "bg-red-500" },
  { name: "Reporting", count: 1, icon: TrendingUp, color: "bg-yellow-500" }
];

const statusStats = [
  { status: "completed", count: 8, color: "bg-green-500" },
  { status: "in_progress", count: 1, color: "bg-yellow-500" },
  { status: "pending", count: 1, color: "bg-blue-500" },
  { status: "failed", count: 0, color: "bg-red-500" }
];

export default function AdminActivity() {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "text-green-600 bg-green-50 dark:bg-green-950";
      case "in_progress": return "text-yellow-600 bg-yellow-50 dark:bg-yellow-950";
      case "pending": return "text-blue-600 bg-blue-50 dark:bg-blue-950";
      case "failed": return "text-red-600 bg-red-50 dark:bg-red-950";
      default: return "text-gray-600 bg-gray-50 dark:bg-gray-950";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed": return <CheckCircle className="h-4 w-4" />;
      case "in_progress": return <Clock className="h-4 w-4" />;
      case "pending": return <Clock className="h-4 w-4" />;
      case "failed": return <XCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-800 dark:bg-red-950 dark:text-red-300";
      case "medium": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-950 dark:text-yellow-300";
      case "low": return "bg-green-100 text-green-800 dark:bg-green-950 dark:text-green-300";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-950 dark:text-gray-300";
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case "student_registration": return <Users className="h-4 w-4" />;
      case "document_verification": return <FileText className="h-4 w-4" />;
      case "payment_processing": return <DollarSign className="h-4 w-4" />;
      case "course_management": return <BookOpen className="h-4 w-4" />;
      case "staff_management": return <Users className="h-4 w-4" />;
      case "content_update": return <BookOpen className="h-4 w-4" />;
      case "post_management": return <MessageSquare className="h-4 w-4" />;
      case "class_scheduling": return <Video className="h-4 w-4" />;
      case "security_monitoring": return <Shield className="h-4 w-4" />;
      case "data_export": return <TrendingUp className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const filteredActivities = adminActivities.filter(activity => {
    const categoryMatch = selectedCategory === "all" || activity.category === selectedCategory;
    const statusMatch = selectedStatus === "all" || activity.status === selectedStatus;
    return categoryMatch && statusMatch;
  });

  return (
    <AdminDashboardLayout>
      <div className="flex flex-1 flex-col gap-6 p-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Admin Activity</h1>
            <p className="text-muted-foreground">
              Monitor all activities performed by Branch Admins across institutes
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Advanced Filter
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export Logs
            </Button>
          </div>
        </div>

        {/* Activity Overview Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Activities</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{adminActivities.length}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+15%</span> from yesterday
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Admins</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {new Set(adminActivities.map(a => a.adminName)).size}
              </div>
              <p className="text-xs text-muted-foreground">
                Across {new Set(adminActivities.map(a => a.institute)).size} institutes
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round((adminActivities.filter(a => a.status === "completed").length / adminActivities.length) * 100)}%
              </div>
              <p className="text-xs text-muted-foreground">
                {adminActivities.filter(a => a.status === "completed").length} of {adminActivities.length} completed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg. Duration</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">18 min</div>
              <p className="text-xs text-muted-foreground">
                Per activity
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Status Overview */}
        <div className="grid gap-6 md:grid-cols-3">
          {/* Category Filter */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Activity Categories
              </CardTitle>
              <CardDescription>
                Filter by activity type
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                variant={selectedCategory === "all" ? "default" : "outline"}
                size="sm"
                className="w-full justify-start"
                onClick={() => setSelectedCategory("all")}
              >
                All Categories ({adminActivities.length})
              </Button>
              {activityCategories.map((category) => (
                <Button
                  key={category.name}
                  variant={selectedCategory === category.name ? "default" : "outline"}
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => setSelectedCategory(category.name)}
                >
                  <category.icon className="h-4 w-4 mr-2" />
                  {category.name} ({category.count})
                </Button>
              ))}
            </CardContent>
          </Card>

          {/* Status Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Status Overview
              </CardTitle>
              <CardDescription>
                Activity completion status
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {statusStats.map((stat) => (
                <div key={stat.status} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`w-3 h-3 rounded-full ${stat.color}`}></div>
                    <span className="text-sm font-medium capitalize">
                      {stat.status.replace('_', ' ')}
                    </span>
                  </div>
                  <div className="text-sm font-bold">{stat.count}</div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Quick Actions
              </CardTitle>
              <CardDescription>
                Common administrative tasks
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Eye className="h-4 w-4 mr-2" />
                View All Logs
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Review Alerts
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <TrendingUp className="h-4 w-4 mr-2" />
                Performance Report
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Shield className="h-4 w-4 mr-2" />
                Security Audit
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Activity Timeline */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Recent Admin Activities
            </CardTitle>
            <CardDescription>
              Real-time monitoring of all Branch Admin activities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredActivities.map((activity) => (
                <div key={activity.id} className="flex items-start gap-4 p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                  {/* Activity Icon */}
                  <div className="flex-shrink-0 w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                    {getActionIcon(activity.action)}
                  </div>

                  {/* Activity Details */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-medium">{activity.actionLabel}</h3>
                          <Badge variant="outline" className={getPriorityColor(activity.priority)}>
                            {activity.priority}
                          </Badge>
                          <Badge className={getStatusColor(activity.status)}>
                            {getStatusIcon(activity.status)}
                            {activity.status.replace('_', ' ')}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{activity.description}</p>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Users className="h-3 w-3" />
                            {activity.adminName}
                          </span>
                          <span className="flex items-center gap-1">
                            <Building2 className="h-3 w-3" />
                            {activity.institute}
                          </span>
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {activity.timestamp}
                          </span>
                          <span className="flex items-center gap-1">
                            <Activity className="h-3 w-3" />
                            {activity.duration}
                          </span>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-2 ml-4">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Activity Details (Expandable) */}
                    <div className="mt-3 p-3 bg-muted/30 rounded-lg">
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
                        {Object.entries(activity.details).map(([key, value]) => (
                          <div key={key}>
                            <div className="font-medium text-muted-foreground capitalize">
                              {key.replace(/([A-Z])/g, ' $1').trim()}
                            </div>
                            <div className="font-medium">{value}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminDashboardLayout>
  );
}

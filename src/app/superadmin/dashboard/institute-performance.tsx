import React from 'react';
import AdminDashboardLayout from "@/layouts/admin-dashboard-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Building2, 
  Users, 
  GraduationCap, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Star,
  Award,
  Target,
  BarChart3,
  Filter,
  Download,
  Eye,
  MoreHorizontal
} from "lucide-react";

// Mock data - in a real app, this would come from API calls
const instituteData = [
  {
    id: 1,
    name: "Nihon Institute of Technology",
    location: "Tokyo, Japan",
    status: "active",
    performance: 94,
    totalStudents: 1250,
    totalStaff: 89,
    completionRate: 87,
    satisfactionScore: 4.6,
    lastUpdated: "2 hours ago",
    growth: "+12%",
    growthType: "positive",
    keyMetrics: {
      enrollment: 1250,
      graduation: 1087,
      employment: 89,
      research: 23
    }
  },
  {
    id: 2,
    name: "Osaka Business Academy",
    location: "Osaka, Japan",
    status: "active",
    performance: 87,
    totalStudents: 890,
    totalStaff: 67,
    completionRate: 82,
    satisfactionScore: 4.3,
    lastUpdated: "4 hours ago",
    growth: "+8%",
    growthType: "positive",
    keyMetrics: {
      enrollment: 890,
      graduation: 730,
      employment: 76,
      research: 18
    }
  },
  {
    id: 3,
    name: "Kyoto Learning Center",
    location: "Kyoto, Japan",
    status: "active",
    performance: 91,
    totalStudents: 1100,
    totalStaff: 78,
    completionRate: 89,
    satisfactionScore: 4.7,
    lastUpdated: "1 hour ago",
    growth: "+15%",
    growthType: "positive",
    keyMetrics: {
      enrollment: 1100,
      graduation: 979,
      employment: 92,
      research: 31
    }
  },
  {
    id: 4,
    name: "Yokohama Education Institute",
    location: "Yokohama, Japan",
    status: "warning",
    performance: 76,
    totalStudents: 650,
    totalStaff: 45,
    completionRate: 71,
    satisfactionScore: 3.9,
    lastUpdated: "6 hours ago",
    growth: "-3%",
    growthType: "negative",
    keyMetrics: {
      enrollment: 650,
      graduation: 462,
      employment: 58,
      research: 12
    }
  },
  {
    id: 5,
    name: "Sapporo Technical College",
    location: "Sapporo, Japan",
    status: "active",
    performance: 89,
    totalStudents: 980,
    totalStaff: 72,
    completionRate: 85,
    satisfactionScore: 4.4,
    lastUpdated: "3 hours ago",
    growth: "+9%",
    growthType: "positive",
    keyMetrics: {
      enrollment: 980,
      graduation: 833,
      employment: 81,
      research: 19
    }
  }
];

const performanceCategories = [
  { name: "Excellent (90-100)", count: 2, color: "bg-green-500" },
  { name: "Good (80-89)", count: 2, color: "bg-blue-500" },
  { name: "Average (70-79)", count: 1, color: "bg-yellow-500" },
  { name: "Below Average (<70)", count: 0, color: "bg-red-500" }
];

const topPerformers = [
  { name: "Nihon Institute of Technology", score: 94, trend: "****%" },
  { name: "Kyoto Learning Center", score: 91, trend: "****%" },
  { name: "Sapporo Technical College", score: 89, trend: "****%" }
];

export default function InstitutePerformance() {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "text-green-600 bg-green-50 dark:bg-green-950";
      case "warning": return "text-yellow-600 bg-yellow-50 dark:bg-yellow-950";
      case "inactive": return "text-red-600 bg-red-50 dark:bg-red-950";
      default: return "text-gray-600 bg-gray-50 dark:bg-gray-950";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active": return <CheckCircle className="h-4 w-4" />;
      case "warning": return <AlertTriangle className="h-4 w-4" />;
      case "inactive": return <Clock className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getGrowthIcon = (type: string) => {
    return type === "positive" ? 
      <TrendingUp className="h-4 w-4 text-green-600" /> : 
      <TrendingDown className="h-4 w-4 text-red-600" />;
  };

  const getPerformanceColor = (score: number) => {
    if (score >= 90) return "text-green-600";
    if (score >= 80) return "text-blue-600";
    if (score >= 70) return "text-yellow-600";
    return "text-red-600";
  };

  const getPerformanceBg = (score: number) => {
    if (score >= 90) return "bg-green-50 dark:bg-green-950";
    if (score >= 80) return "bg-blue-50 dark:bg-blue-950";
    if (score >= 70) return "bg-yellow-50 dark:bg-yellow-950";
    return "bg-red-50 dark:bg-red-950";
  };

  return (
    <AdminDashboardLayout>
      <div className="flex flex-1 flex-col gap-6 p-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Institute Performance</h1>
            <p className="text-muted-foreground">
              Monitor and analyze performance metrics across all institutes
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Overview Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Institutes</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{instituteData.length}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+2</span> new this quarter
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Performance</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(instituteData.reduce((acc, inst) => acc + inst.performance, 0) / instituteData.length)}%
              </div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+3.2%</span> from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Students</CardTitle>
              <GraduationCap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {instituteData.reduce((acc, inst) => acc + inst.totalStudents, 0).toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+8.5%</span> enrollment growth
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Status</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {instituteData.filter(inst => inst.status === "active").length}
              </div>
              <p className="text-xs text-muted-foreground">
                of {instituteData.length} institutes
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Performance Distribution and Top Performers */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Performance Distribution */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Performance Distribution
              </CardTitle>
              <CardDescription>
                Breakdown of institutes by performance level
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {performanceCategories.map((category) => (
                <div key={category.name} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${category.color}`}></div>
                    <span className="text-sm font-medium">{category.name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-bold">{category.count}</span>
                    <span className="text-xs text-muted-foreground">institutes</span>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Top Performers */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" />
                Top Performers
              </CardTitle>
              <CardDescription>
                Institutes with highest performance scores
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {topPerformers.map((performer, index) => (
                <div key={performer.name} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <span className="text-sm font-bold text-primary">{index + 1}</span>
                    </div>
                    <div>
                      <p className="text-sm font-medium">{performer.name}</p>
                      <p className="text-xs text-muted-foreground">{performer.trend}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-green-600">{performer.score}%</div>
                    <div className="text-xs text-muted-foreground">Performance</div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Institute Performance Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Institute Performance Details
            </CardTitle>
            <CardDescription>
              Comprehensive view of all institute metrics and performance indicators
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-3 font-medium">Institute</th>
                    <th className="text-left p-3 font-medium">Status</th>
                    <th className="text-left p-3 font-medium">Performance</th>
                    <th className="text-left p-3 font-medium">Students</th>
                    <th className="text-left p-3 font-medium">Staff</th>
                    <th className="text-left p-3 font-medium">Completion Rate</th>
                    <th className="text-left p-3 font-medium">Satisfaction</th>
                    <th className="text-left p-3 font-medium">Growth</th>
                    <th className="text-left p-3 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {instituteData.map((institute) => (
                    <tr key={institute.id} className="border-b hover:bg-muted/50">
                      <td className="p-3">
                        <div>
                          <div className="font-medium">{institute.name}</div>
                          <div className="text-sm text-muted-foreground">{institute.location}</div>
                        </div>
                      </td>
                      <td className="p-3">
                        <div className={`inline-flex items-center gap-2 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(institute.status)}`}>
                          {getStatusIcon(institute.status)}
                          {institute.status}
                        </div>
                      </td>
                      <td className="p-3">
                        <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${getPerformanceBg(institute.performance)} ${getPerformanceColor(institute.performance)}`}>
                          <Target className="h-4 w-4" />
                          {institute.performance}%
                        </div>
                      </td>
                      <td className="p-3">
                        <div className="text-sm font-medium">{institute.totalStudents.toLocaleString()}</div>
                      </td>
                      <td className="p-3">
                        <div className="text-sm font-medium">{institute.totalStaff}</div>
                      </td>
                      <td className="p-3">
                        <div className="text-sm font-medium">{institute.completionRate}%</div>
                      </td>
                      <td className="p-3">
                        <div className="flex items-center gap-1">
                          <span className="text-sm font-medium">{institute.satisfactionScore}</span>
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        </div>
                      </td>
                      <td className="p-3">
                        <div className="flex items-center gap-1">
                          {getGrowthIcon(institute.growthType)}
                          <span className={`text-sm font-medium ${institute.growthType === "positive" ? "text-green-600" : "text-red-600"}`}>
                            {institute.growth}
                          </span>
                        </div>
                      </td>
                      <td className="p-3">
                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Key Performance Indicators */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {instituteData.slice(0, 4).map((institute) => (
            <Card key={institute.id}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium truncate">{institute.name}</CardTitle>
                  <div className={`w-2 h-2 rounded-full ${institute.status === "active" ? "bg-green-500" : "bg-yellow-500"}`}></div>
                </div>
                <CardDescription className="text-xs">{institute.location}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div>
                    <div className="text-muted-foreground">Enrollment</div>
                    <div className="font-medium">{institute.keyMetrics.enrollment}</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Graduation</div>
                    <div className="font-medium">{institute.keyMetrics.graduation}</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Employment</div>
                    <div className="font-medium">{institute.keyMetrics.employment}%</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground">Research</div>
                    <div className="font-medium">{institute.keyMetrics.research}</div>
                  </div>
                </div>
                <div className="pt-2 border-t">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground">Performance</span>
                    <span className={`font-medium ${getPerformanceColor(institute.performance)}`}>
                      {institute.performance}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                    <div 
                      className={`h-2 rounded-full ${getPerformanceColor(institute.performance)}`}
                      style={{ width: `${institute.performance}%` }}
                    ></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </AdminDashboardLayout>
  );
}

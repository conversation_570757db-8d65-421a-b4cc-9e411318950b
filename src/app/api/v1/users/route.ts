// app/api/v1/users/route.ts
import { NextRequest, NextResponse } from "next/server";
import * as userService from "@/services/users/users.service";

export async function GET(req: NextRequest) {
    try {
        const users = await userService.getAllUsers();
        return NextResponse.json({ status: "success", data: users });
    } catch (error: any) {
        return NextResponse.json(
            { status: "failed", message: error.message || "Internal Server Error" },
            { status: 500 }
        );
    }
}

export async function POST(req: NextRequest) {
    try {
        const body = await req.json();
        const { name, email, phone, password, role_id, status } = body;

        const user = await userService.createUsersService({
            name,
            email,
            phone,
            password,
            role_id,
            status,
        });

        return NextResponse.json({ status: "success", data: user }, { status: 201 });
    } catch (error: any) {
        if (error.message === "USER_EXISTS") {
            return NextResponse.json(
                { statusTitle: "Duplicate Entry", message: "User already exists" },
                { status: 409 }
            );
        }
        return NextResponse.json(
            { status: "failed", message: error.message || "Internal Server Error" },
            { status: 500 }
        );
    }
}

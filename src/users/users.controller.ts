export const runtime = "edge";

import { createUsersService, getAllUsers } from "@/services/users/users.service";
import { catchAsync } from "@/utils/catchAsync";
import { NextApiRequest, NextApiResponse } from "next";

export const getAllUsersList = catchAsync(async (req: NextApiRequest, res: NextApiResponse) => {
    const users = await getAllUsers();
    res.status(200).json(users);
})

export const createUsers = catchAsync(async (req: NextApiRequest, res: NextApiResponse) => {
    const { name, email, phone, password, role_id, status } = req.body;
    const users = await createUsersService({
        name, email, phone, password, role_id, status
    });

    res.status(200).json(users);
})